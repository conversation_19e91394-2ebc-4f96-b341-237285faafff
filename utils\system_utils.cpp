/*
 * QuadFly Flight Controller - System Utilities Implementation
 * 
 * System-level utility functions and monitoring for ESP32
 */

#include "system_utils.h"
#include <esp_heap_caps.h>
#include <esp_task_wdt.h>
#include <esp_system.h>
#include <esp_timer.h>
#include <soc/rtc.h>

// Global system monitor instance
static SystemMonitor_t g_system_monitor = {0};
static PerformanceMonitor_t g_performance_monitor = {0};
static bool system_monitoring_initialized = false;
static SemaphoreHandle_t system_monitor_mutex = NULL;

// Task monitoring arrays
static TaskInfo_t monitored_tasks[10];
static uint8_t task_count = 0;

// Error tracking
static uint32_t error_flags = 0;
static char error_messages[10][64];
static uint8_t error_count = 0;

// Initialize system monitoring
void initializeSystemMonitoring(void) {
  if (system_monitoring_initialized) return;
  
  // Create mutex for thread-safe access
  system_monitor_mutex = xSemaphoreCreateMutex();
  if (system_monitor_mutex == NULL) {
    Serial.println("ERROR: Failed to create system monitor mutex");
    return;
  }
  
  // Initialize system monitor structure
  memset(&g_system_monitor, 0, sizeof(SystemMonitor_t));
  memset(&g_performance_monitor, 0, sizeof(PerformanceMonitor_t));
  
  // Get initial heap information
  g_system_monitor.totalHeap = ESP.getHeapSize();
  g_system_monitor.freeHeap = ESP.getFreeHeap();
  g_system_monitor.minFreeHeap = ESP.getMinFreeHeap();
  
  // Initialize performance monitor
  initializePerformanceMonitor(&g_performance_monitor);
  
  system_monitoring_initialized = true;
  Serial.println("System monitoring initialized successfully");
}

// Update system monitor
void updateSystemMonitor(SystemMonitor_t *monitor) {
  if (!system_monitoring_initialized || monitor == NULL) return;
  
  if (xSemaphoreTake(system_monitor_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
    // Update heap information
    monitor->freeHeap = ESP.getFreeHeap();
    monitor->minFreeHeap = ESP.getMinFreeHeap();
    monitor->totalHeap = ESP.getHeapSize();
    monitor->largestFreeBlock = heap_caps_get_largest_free_block(MALLOC_CAP_8BIT);
    
    // Update uptime
    monitor->uptime = millis();
    
    // Update task count
    monitor->taskCount = uxTaskGetNumberOfTasks();
    
    // Check for memory leaks (simple heuristic)
    static uint32_t last_free_heap = 0;
    if (last_free_heap > 0) {
      int32_t heap_change = (int32_t)monitor->freeHeap - (int32_t)last_free_heap;
      if (heap_change < -1024 && monitor->uptime > 60000) { // Lost >1KB after 1 minute
        monitor->memoryLeakDetected = true;
        monitor->memoryLeakSize += abs(heap_change);
      }
    }
    last_free_heap = monitor->freeHeap;
    
    // Update CPU usage (simplified)
    updateCpuUsage(monitor);
    
    xSemaphoreGive(system_monitor_mutex);
  }
}

// Calculate system health
SystemHealth_t calculateSystemHealth(SystemMonitor_t *monitor) {
  if (monitor == NULL) return SYSTEM_HEALTH_FAILURE;
  
  uint8_t health_score = 100;
  
  // Check heap usage
  float heap_usage = 1.0f - ((float)monitor->freeHeap / (float)monitor->totalHeap);
  if (heap_usage > 0.9f) health_score -= 30;
  else if (heap_usage > 0.8f) health_score -= 20;
  else if (heap_usage > 0.7f) health_score -= 10;
  
  // Check CPU usage
  if (monitor->cpuUsage > 95.0f) health_score -= 25;
  else if (monitor->cpuUsage > 85.0f) health_score -= 15;
  else if (monitor->cpuUsage > 75.0f) health_score -= 10;
  
  // Check for memory leaks
  if (monitor->memoryLeakDetected) health_score -= 20;
  
  // Check task count (too many tasks can indicate problems)
  if (monitor->taskCount > 20) health_score -= 10;
  
  // Return appropriate health level
  if (health_score >= 90) return SYSTEM_HEALTH_EXCELLENT;
  if (health_score >= 75) return SYSTEM_HEALTH_GOOD;
  if (health_score >= 60) return SYSTEM_HEALTH_WARNING;
  if (health_score >= 40) return SYSTEM_HEALTH_CRITICAL;
  if (health_score >= 20) return SYSTEM_HEALTH_EMERGENCY;
  return SYSTEM_HEALTH_FAILURE;
}

// Print system status
void printSystemStatus(SystemMonitor_t *monitor) {
  if (monitor == NULL) return;
  
  Serial.println("=== System Status ===");
  Serial.printf("Uptime: %lu ms\n", monitor->uptime);
  Serial.printf("Free Heap: %lu bytes (%.1f%%)\n", 
                monitor->freeHeap, 
                (float)monitor->freeHeap / monitor->totalHeap * 100.0f);
  Serial.printf("Min Free Heap: %lu bytes\n", monitor->minFreeHeap);
  Serial.printf("Largest Free Block: %lu bytes\n", monitor->largestFreeBlock);
  Serial.printf("CPU Usage: %.1f%%\n", monitor->cpuUsage);
  Serial.printf("Task Count: %u\n", monitor->taskCount);
  Serial.printf("Memory Leak Detected: %s\n", monitor->memoryLeakDetected ? "YES" : "NO");
  if (monitor->memoryLeakDetected) {
    Serial.printf("Estimated Leak Size: %lu bytes\n", monitor->memoryLeakSize);
  }
  
  SystemHealth_t health = calculateSystemHealth(monitor);
  Serial.printf("System Health: %d\n", health);
  Serial.println("====================");
}

// Safe memory allocation
void* safeMalloc(size_t size) {
  if (size == 0) return NULL;
  
  void* ptr = malloc(size);
  if (ptr == NULL) {
    Serial.printf("ERROR: malloc failed for %zu bytes\n", size);
    logError(ERROR_OUT_OF_MEMORY, "malloc failed");
  }
  return ptr;
}

// Safe memory deallocation
void safeFree(void* ptr) {
  if (ptr != NULL) {
    free(ptr);
  }
}

// Safe memory reallocation
void* safeRealloc(void* ptr, size_t size) {
  if (size == 0) {
    safeFree(ptr);
    return NULL;
  }
  
  void* new_ptr = realloc(ptr, size);
  if (new_ptr == NULL && size > 0) {
    Serial.printf("ERROR: realloc failed for %zu bytes\n", size);
    logError(ERROR_OUT_OF_MEMORY, "realloc failed");
  }
  return new_ptr;
}

// Initialize performance monitor
void initializePerformanceMonitor(PerformanceMonitor_t *perfMon) {
  if (perfMon == NULL) return;
  
  memset(perfMon, 0, sizeof(PerformanceMonitor_t));
  perfMon->maxLoopTime = 0;
  perfMon->avgLoopTime = 0;
  perfMon->performanceWarning = false;
}

// Update performance monitor
void updatePerformanceMonitor(PerformanceMonitor_t *perfMon, uint32_t loopTime) {
  if (perfMon == NULL) return;
  
  perfMon->loopCount++;
  perfMon->lastLoopTime = loopTime;
  perfMon->totalLoopTime += loopTime;
  
  if (loopTime > perfMon->maxLoopTime) {
    perfMon->maxLoopTime = loopTime;
  }
  
  perfMon->avgLoopTime = perfMon->totalLoopTime / perfMon->loopCount;
  
  // Check for performance warnings (loop time > 20ms for flight controller)
  if (loopTime > 20000) { // 20ms in microseconds
    perfMon->performanceWarning = true;
  }
}

// Print performance status
void printPerformanceStatus(PerformanceMonitor_t *perfMon) {
  if (perfMon == NULL) return;
  
  Serial.println("=== Performance Status ===");
  Serial.printf("Loop Count: %lu\n", perfMon->loopCount);
  Serial.printf("Last Loop Time: %lu us\n", perfMon->lastLoopTime);
  Serial.printf("Average Loop Time: %lu us\n", perfMon->avgLoopTime);
  Serial.printf("Max Loop Time: %lu us\n", perfMon->maxLoopTime);
  Serial.printf("Performance Warning: %s\n", perfMon->performanceWarning ? "YES" : "NO");
  Serial.println("=========================");
}

// Update CPU usage (simplified implementation)
void updateCpuUsage(SystemMonitor_t *monitor) {
  static uint32_t lastIdleTime = 0;
  static uint32_t lastTotalTime = 0;
  
  // Get current idle task runtime (approximation)
  TaskHandle_t idleTask = xTaskGetIdleTaskHandle();
  uint32_t currentIdleTime = 0; // ESP32 doesn't provide direct access
  uint32_t currentTotalTime = millis();
  
  // Simple CPU usage estimation based on system responsiveness
  // This is a simplified approach - real implementation would need more sophisticated timing
  if (currentTotalTime > lastTotalTime) {
    uint32_t timeDelta = currentTotalTime - lastTotalTime;
    if (timeDelta > 100) { // Update every 100ms
      // Estimate CPU usage based on heap allocation rate and task switching
      float heapPressure = 1.0f - ((float)monitor->freeHeap / (float)monitor->totalHeap);
      float taskPressure = (float)monitor->taskCount / 20.0f; // Assume 20 is high
      
      monitor->cpuUsage = (heapPressure * 50.0f) + (taskPressure * 30.0f);
      if (monitor->cpuUsage > 100.0f) monitor->cpuUsage = 100.0f;
      
      lastTotalTime = currentTotalTime;
    }
  }
}

// Get system uptime
uint32_t getSystemUptime(void) {
  return millis();
}

// Get microseconds since boot
uint64_t getMicrosecondsSinceBoot(void) {
  return esp_timer_get_time();
}

// String utilities
void safeStringCopy(char *dest, const char *src, size_t destSize) {
  if (dest == NULL || src == NULL || destSize == 0) return;
  
  strncpy(dest, src, destSize - 1);
  dest[destSize - 1] = '\0';
}

void safeStringCat(char *dest, const char *src, size_t destSize) {
  if (dest == NULL || src == NULL || destSize == 0) return;
  
  size_t destLen = strlen(dest);
  if (destLen < destSize - 1) {
    strncat(dest, src, destSize - destLen - 1);
  }
}

// Data validation
bool isValidFloat(float value) {
  return !isnan(value) && !isinf(value);
}

bool isValidRange(float value, float min, float max) {
  return isValidFloat(value) && value >= min && value <= max;
}

bool isValidPointer(void *ptr) {
  return ptr != NULL;
}

// Calculate simple checksum
uint16_t calculateChecksum(uint8_t *data, size_t length) {
  if (data == NULL || length == 0) return 0;
  
  uint16_t checksum = 0;
  for (size_t i = 0; i < length; i++) {
    checksum += data[i];
  }
  return checksum;
}

// Validate checksum
bool validateChecksum(uint8_t *data, size_t length, uint16_t expectedChecksum) {
  return calculateChecksum(data, length) == expectedChecksum;
}

// Error logging
void logError(SystemError_t error, const char *message) {
  if (error_count < 10) {
    safeStringCopy(error_messages[error_count], message, 64);
    error_count++;
  }
  error_flags |= (1 << error);
  
  Serial.printf("ERROR [%d]: %s\n", error, message);
}

// Clear error
void clearError(SystemError_t error) {
  error_flags &= ~(1 << error);
}

// Check if error exists
bool hasError(SystemError_t error) {
  return (error_flags & (1 << error)) != 0;
}

// Print error log
void printErrorLog(void) {
  Serial.println("=== Error Log ===");
  for (uint8_t i = 0; i < error_count; i++) {
    Serial.printf("Error %d: %s\n", i, error_messages[i]);
  }
  Serial.printf("Error Flags: 0x%08lX\n", error_flags);
  Serial.println("================");
}

// Get global system monitor
SystemMonitor_t* getSystemMonitor(void) {
  return &g_system_monitor;
}

// Get global performance monitor
PerformanceMonitor_t* getPerformanceMonitor(void) {
  return &g_performance_monitor;
}
