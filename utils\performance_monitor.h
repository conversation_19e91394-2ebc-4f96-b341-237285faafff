/*
 * QuadFly Flight Controller - Performance Monitor Header
 * 
 * Real-time performance monitoring and optimization
 */

#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <esp_timer.h>
#include <esp_system.h>

// Task priority definitions (higher number = higher priority)
#define TASK_PRIORITY_CRITICAL    25  // Critical safety tasks
#define TASK_PRIORITY_HIGH        20  // Control loop
#define TASK_PRIORITY_NORMAL      15  // Sensor reading
#define TASK_PRIORITY_LOW         10  // Telemetry, logging
#define TASK_PRIORITY_BACKGROUND   5  // Housekeeping

// Performance monitoring configuration
#define MAX_MONITORED_TASKS       10
#define PERFORMANCE_SAMPLE_SIZE   100
#define CPU_USAGE_UPDATE_RATE_MS  100
#define STACK_MONITOR_INTERVAL_MS 1000

// Performance metrics structure
typedef struct {
  uint32_t execution_time_us;
  uint32_t max_execution_time_us;
  uint32_t min_execution_time_us;
  uint32_t avg_execution_time_us;
  uint32_t total_executions;
  uint32_t missed_deadlines;
  float cpu_usage_percent;
  bool deadline_warning;
} TaskPerformance_t;

// System performance metrics
typedef struct {
  float overall_cpu_usage;
  uint32_t free_heap;
  uint32_t min_free_heap;
  uint32_t context_switches;
  uint32_t interrupts_per_second;
  uint32_t max_interrupt_latency_us;
  uint32_t avg_interrupt_latency_us;
  bool performance_warning;
  bool critical_performance_alert;
} SystemPerformance_t;

// Task monitoring structure
typedef struct {
  TaskHandle_t handle;
  char name[16];
  uint8_t priority;
  uint32_t stack_size;
  uint32_t free_stack;
  uint32_t deadline_us;
  TaskPerformance_t performance;
  bool enabled;
} MonitoredTask_t;

// Interrupt performance monitoring
typedef struct {
  uint32_t interrupt_count;
  uint32_t total_time_us;
  uint32_t max_time_us;
  uint32_t min_time_us;
  uint32_t avg_time_us;
  bool enabled;
} InterruptPerformance_t;

// DMA performance monitoring
typedef struct {
  uint32_t transfers_completed;
  uint32_t transfer_errors;
  uint32_t avg_transfer_time_us;
  uint32_t max_transfer_time_us;
  float throughput_mbps;
  bool enabled;
} DMAPerformance_t;

// Performance optimization settings
typedef struct {
  bool enable_cpu_monitoring;
  bool enable_task_monitoring;
  bool enable_interrupt_monitoring;
  bool enable_dma_monitoring;
  bool enable_automatic_optimization;
  uint8_t monitoring_level;  // 0=minimal, 1=normal, 2=detailed
  uint32_t warning_threshold_percent;
  uint32_t critical_threshold_percent;
} PerformanceConfig_t;

// Function declarations

// Initialization and configuration
bool PerformanceMonitor_Init(void);
void PerformanceMonitor_DeInit(void);
bool PerformanceMonitor_Configure(const PerformanceConfig_t* config);
void PerformanceMonitor_SetMonitoringLevel(uint8_t level);

// Task monitoring
bool PerformanceMonitor_RegisterTask(TaskHandle_t task, const char* name, uint32_t deadline_us);
bool PerformanceMonitor_UnregisterTask(TaskHandle_t task);
void PerformanceMonitor_TaskStart(TaskHandle_t task);
void PerformanceMonitor_TaskEnd(TaskHandle_t task);
void PerformanceMonitor_UpdateTaskMetrics(void);

// CPU usage monitoring
void PerformanceMonitor_StartCpuMonitoring(void);
void PerformanceMonitor_StopCpuMonitoring(void);
float PerformanceMonitor_GetCpuUsage(void);
float PerformanceMonitor_GetTaskCpuUsage(TaskHandle_t task);
void PerformanceMonitor_UpdateCpuMetrics(void);

// Interrupt monitoring
void PerformanceMonitor_InterruptStart(uint8_t interrupt_id);
void PerformanceMonitor_InterruptEnd(uint8_t interrupt_id);
InterruptPerformance_t PerformanceMonitor_GetInterruptMetrics(uint8_t interrupt_id);
void PerformanceMonitor_ResetInterruptMetrics(uint8_t interrupt_id);

// DMA monitoring
void PerformanceMonitor_DMATransferStart(uint8_t channel);
void PerformanceMonitor_DMATransferEnd(uint8_t channel, bool success);
DMAPerformance_t PerformanceMonitor_GetDMAMetrics(uint8_t channel);
void PerformanceMonitor_ResetDMAMetrics(uint8_t channel);

// Stack monitoring
void PerformanceMonitor_CheckStackUsage(void);
uint32_t PerformanceMonitor_GetStackHighWaterMark(TaskHandle_t task);
bool PerformanceMonitor_IsStackOverflowRisk(TaskHandle_t task);

// Performance analysis
SystemPerformance_t PerformanceMonitor_GetSystemMetrics(void);
TaskPerformance_t PerformanceMonitor_GetTaskMetrics(TaskHandle_t task);
bool PerformanceMonitor_IsPerformanceWarning(void);
bool PerformanceMonitor_IsCriticalAlert(void);

// Optimization recommendations
typedef enum {
  OPTIMIZATION_NONE = 0,
  OPTIMIZATION_INCREASE_PRIORITY,
  OPTIMIZATION_DECREASE_PRIORITY,
  OPTIMIZATION_INCREASE_STACK,
  OPTIMIZATION_OPTIMIZE_CODE,
  OPTIMIZATION_ENABLE_DMA,
  OPTIMIZATION_REDUCE_FREQUENCY,
  OPTIMIZATION_SPLIT_TASK
} OptimizationRecommendation_t;

OptimizationRecommendation_t PerformanceMonitor_GetRecommendation(TaskHandle_t task);
void PerformanceMonitor_ApplyOptimization(TaskHandle_t task, OptimizationRecommendation_t optimization);

// Reporting and logging
void PerformanceMonitor_PrintReport(void);
void PerformanceMonitor_PrintTaskReport(TaskHandle_t task);
void PerformanceMonitor_PrintSystemReport(void);
void PerformanceMonitor_LogMetrics(void);

// Real-time constraints
bool PerformanceMonitor_SetDeadline(TaskHandle_t task, uint32_t deadline_us);
bool PerformanceMonitor_CheckDeadline(TaskHandle_t task);
uint32_t PerformanceMonitor_GetWorstCaseExecutionTime(TaskHandle_t task);
float PerformanceMonitor_GetDeadlineMissRate(TaskHandle_t task);

// Profiling utilities
#define PROFILE_FUNCTION_START() \
  uint32_t _profile_start = esp_timer_get_time()

#define PROFILE_FUNCTION_END(task_handle) \
  do { \
    uint32_t _profile_end = esp_timer_get_time(); \
    uint32_t _profile_duration = _profile_end - _profile_start; \
    PerformanceMonitor_RecordExecution(task_handle, _profile_duration); \
  } while(0)

#define PROFILE_BLOCK_START(name) \
  uint32_t _profile_##name##_start = esp_timer_get_time()

#define PROFILE_BLOCK_END(name) \
  do { \
    uint32_t _profile_##name##_end = esp_timer_get_time(); \
    uint32_t _profile_##name##_duration = _profile_##name##_end - _profile_##name##_start; \
    Serial.printf("PROFILE %s: %u us\n", #name, _profile_##name##_duration); \
  } while(0)

// Internal functions
void PerformanceMonitor_RecordExecution(TaskHandle_t task, uint32_t execution_time_us);
void PerformanceMonitor_UpdateStatistics(void);
void PerformanceMonitor_CheckThresholds(void);

// Callback types
typedef void (*PerformanceWarningCallback_t)(TaskHandle_t task, const char* message);
typedef void (*CriticalAlertCallback_t)(const char* message);

// Callback registration
bool PerformanceMonitor_RegisterWarningCallback(PerformanceWarningCallback_t callback);
bool PerformanceMonitor_RegisterCriticalCallback(CriticalAlertCallback_t callback);

// Advanced features
void PerformanceMonitor_EnableAdaptivePriorities(bool enable);
void PerformanceMonitor_EnableLoadBalancing(bool enable);
void PerformanceMonitor_SetCpuAffinityOptimization(bool enable);

// Memory performance
typedef struct {
  uint32_t allocations_per_second;
  uint32_t deallocations_per_second;
  uint32_t fragmentation_percent;
  uint32_t largest_free_block;
  uint32_t allocation_failures;
} MemoryPerformance_t;

MemoryPerformance_t PerformanceMonitor_GetMemoryMetrics(void);
void PerformanceMonitor_MonitorMemoryAllocation(void* ptr, size_t size);
void PerformanceMonitor_MonitorMemoryDeallocation(void* ptr);

#endif // PERFORMANCE_MONITOR_H
