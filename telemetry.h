/*
 * QuadFly Flight Controller - Telemetry System Header
 * 
 * Professional telemetry data transmission and command processing
 */

#ifndef TELEMETRY_H
#define TELEMETRY_H

#include "config.h"

// Telemetry message types
typedef enum {
  TELEM_ATTITUDE = 0x01,
  TELEM_GPS = 0x02,
  TELEM_BATTERY = 0x03,
  TELEM_SYSTEM_STATUS = 0x04,
  TELEM_SENSOR_DATA = 0x05,
  TELEM_MOTOR_DATA = 0x06,
  TELEM_PID_DATA = 0x07,
  TELEM_HEARTBEAT = 0xFF
} TelemMessageType_t;

// Function declarations
void initializeTelemetry(void);
void sendTelemetryData(FlightData_t *flightData, SystemState_t *systemState);
void processTelemetryCommands(SystemState_t *systemState, ConfigData_t *config);
void sendHeartbeat(SystemState_t *systemState);
void sendAttitudeData(AttitudeData_t *attitude);
void sendGPSData(GPSData_t *gps);
void sendBatteryData(BatteryData_t *battery);
void sendSystemStatus(SystemState_t *systemState);

#endif // TELEMETRY_H