/*
 * QuadFly Flight Controller - PWM HAL Implementation
 * 
 * ESP32-specific PWM hardware abstraction
 */

#include "hal_pwm.h"
#include <driver/ledc.h>

// PWM channel tracking
static HAL_PWM_Channel pwm_channels[16];
static bool pwm_initialized = false;

// Initialize PWM subsystem
HAL_StatusTypeDef HAL_PWM_Init(void) {
  if (pwm_initialized) {
    return HAL_OK;
  }
  
  // Initialize channel tracking
  for (uint8_t i = 0; i < 16; i++) {
    pwm_channels[i].enabled = false;
    pwm_channels[i].pin = 255; // Invalid pin
    pwm_channels[i].channel = i;
    pwm_channels[i].frequency = 0;
    pwm_channels[i].resolution = 0;
    pwm_channels[i].duty_cycle = 0;
  }
  
  pwm_initialized = true;
  return HAL_OK;
}

// Deinitialize PWM subsystem
HAL_StatusTypeDef HAL_PWM_DeInit(void) {
  if (!pwm_initialized) {
    return HAL_OK;
  }
  
  // Stop all PWM channels
  for (uint8_t i = 0; i < 16; i++) {
    if (pwm_channels[i].enabled) {
      HAL_PWM_Stop(i);
    }
  }
  
  pwm_initialized = false;
  return HAL_OK;
}

// Attach pin to PWM channel
HAL_StatusTypeDef HAL_PWM_AttachPin(uint8_t pin, uint32_t frequency, uint8_t resolution) {
  if (!pwm_initialized) {
    return HAL_ERROR;
  }
  
  // Find available channel
  uint8_t channel = HAL_PWM_GetAvailableChannel();
  if (channel >= 16) {
    return HAL_ERROR; // No available channels
  }
  
  // Configure LEDC timer
  ledc_timer_config_t timer_config = {
    .speed_mode = LEDC_LOW_SPEED_MODE,
    .timer_num = (ledc_timer_t)(channel / 4), // 4 channels per timer
    .duty_resolution = (ledc_timer_bit_t)resolution,
    .freq_hz = frequency,
    .clk_cfg = LEDC_AUTO_CLK
  };
  
  esp_err_t timer_result = ledc_timer_config(&timer_config);
  if (timer_result != ESP_OK) {
    return HAL_ERROR;
  }
  
  // Configure LEDC channel
  ledc_channel_config_t channel_config = {
    .gpio_num = pin,
    .speed_mode = LEDC_LOW_SPEED_MODE,
    .channel = (ledc_channel_t)channel,
    .timer_sel = (ledc_timer_t)(channel / 4),
    .duty = 0,
    .hpoint = 0
  };
  
  esp_err_t channel_result = ledc_channel_config(&channel_config);
  if (channel_result != ESP_OK) {
    return HAL_ERROR;
  }
  
  // Update channel tracking
  pwm_channels[channel].pin = pin;
  pwm_channels[channel].frequency = frequency;
  pwm_channels[channel].resolution = resolution;
  pwm_channels[channel].duty_cycle = 0;
  pwm_channels[channel].enabled = true;
  
  return HAL_OK;
}

// Detach pin from PWM
HAL_StatusTypeDef HAL_PWM_DetachPin(uint8_t pin) {
  if (!pwm_initialized) {
    return HAL_ERROR;
  }
  
  // Find channel for this pin
  for (uint8_t i = 0; i < 16; i++) {
    if (pwm_channels[i].enabled && pwm_channels[i].pin == pin) {
      // Stop the channel
      ledc_stop(LEDC_LOW_SPEED_MODE, (ledc_channel_t)i, 0);
      
      // Clear channel tracking
      pwm_channels[i].enabled = false;
      pwm_channels[i].pin = 255;
      pwm_channels[i].frequency = 0;
      pwm_channels[i].resolution = 0;
      pwm_channels[i].duty_cycle = 0;
      
      return HAL_OK;
    }
  }
  
  return HAL_ERROR; // Pin not found
}

// Get channel for pin
uint8_t HAL_PWM_GetChannel(uint8_t pin) {
  if (!pwm_initialized) {
    return 255; // Invalid channel
  }
  
  for (uint8_t i = 0; i < 16; i++) {
    if (pwm_channels[i].enabled && pwm_channels[i].pin == pin) {
      return i;
    }
  }
  
  return 255; // Pin not found
}

// Check if channel is used
bool HAL_PWM_IsChannelUsed(uint8_t channel) {
  if (!pwm_initialized || channel >= 16) {
    return false;
  }
  
  return pwm_channels[channel].enabled;
}

// Start PWM output
HAL_StatusTypeDef HAL_PWM_Start(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return HAL_ERROR;
  }
  
  // PWM starts automatically when duty cycle is set
  return HAL_OK;
}

// Stop PWM output
HAL_StatusTypeDef HAL_PWM_Stop(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return HAL_ERROR;
  }
  
  esp_err_t result = ledc_stop(LEDC_LOW_SPEED_MODE, (ledc_channel_t)channel, 0);
  return (result == ESP_OK) ? HAL_OK : HAL_ERROR;
}

// Set duty cycle
HAL_StatusTypeDef HAL_PWM_SetDutyCycle(uint8_t channel, uint32_t duty_cycle) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return HAL_ERROR;
  }
  
  // Validate duty cycle range
  uint32_t max_duty = (1 << pwm_channels[channel].resolution) - 1;
  if (duty_cycle > max_duty) {
    duty_cycle = max_duty;
  }
  
  esp_err_t result = ledc_set_duty(LEDC_LOW_SPEED_MODE, (ledc_channel_t)channel, duty_cycle);
  if (result != ESP_OK) {
    return HAL_ERROR;
  }
  
  result = ledc_update_duty(LEDC_LOW_SPEED_MODE, (ledc_channel_t)channel);
  if (result != ESP_OK) {
    return HAL_ERROR;
  }
  
  pwm_channels[channel].duty_cycle = duty_cycle;
  return HAL_OK;
}

// Set duty cycle as percentage
HAL_StatusTypeDef HAL_PWM_SetDutyCyclePercent(uint8_t channel, float percent) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return HAL_ERROR;
  }
  
  // Clamp percentage
  if (percent < 0.0f) percent = 0.0f;
  if (percent > 100.0f) percent = 100.0f;
  
  // Convert to duty cycle value
  uint32_t max_duty = (1 << pwm_channels[channel].resolution) - 1;
  uint32_t duty_cycle = (uint32_t)(percent / 100.0f * max_duty);
  
  return HAL_PWM_SetDutyCycle(channel, duty_cycle);
}

// Set pulse width in microseconds
HAL_StatusTypeDef HAL_PWM_SetPulseWidth(uint8_t channel, uint32_t pulse_width_us) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return HAL_ERROR;
  }
  
  // Calculate duty cycle from pulse width
  uint32_t period_us = 1000000 / pwm_channels[channel].frequency;
  float duty_percent = (float)pulse_width_us / (float)period_us * 100.0f;
  
  return HAL_PWM_SetDutyCyclePercent(channel, duty_percent);
}

// Set frequency
HAL_StatusTypeDef HAL_PWM_SetFrequency(uint8_t channel, uint32_t frequency) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return HAL_ERROR;
  }
  
  esp_err_t result = ledc_set_freq(LEDC_LOW_SPEED_MODE, (ledc_timer_t)(channel / 4), frequency);
  if (result == ESP_OK) {
    pwm_channels[channel].frequency = frequency;
    return HAL_OK;
  }
  
  return HAL_ERROR;
}

// Get duty cycle
uint32_t HAL_PWM_GetDutyCycle(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return 0;
  }
  
  return pwm_channels[channel].duty_cycle;
}

// Get duty cycle as percentage
float HAL_PWM_GetDutyCyclePercent(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return 0.0f;
  }
  
  uint32_t max_duty = (1 << pwm_channels[channel].resolution) - 1;
  return (float)pwm_channels[channel].duty_cycle / (float)max_duty * 100.0f;
}

// Get pulse width
uint32_t HAL_PWM_GetPulseWidth(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return 0;
  }
  
  float duty_percent = HAL_PWM_GetDutyCyclePercent(channel);
  uint32_t period_us = 1000000 / pwm_channels[channel].frequency;
  
  return (uint32_t)(duty_percent / 100.0f * period_us);
}

// Get frequency
uint32_t HAL_PWM_GetFrequency(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return 0;
  }
  
  return pwm_channels[channel].frequency;
}

// Get resolution
uint8_t HAL_PWM_GetResolution(uint8_t channel) {
  if (!pwm_initialized || channel >= 16 || !pwm_channels[channel].enabled) {
    return 0;
  }
  
  return pwm_channels[channel].resolution;
}

// Calculate duty cycle from pulse width
uint32_t HAL_PWM_CalculateDutyCycle(uint32_t pulse_width_us, uint32_t frequency, uint8_t resolution) {
  uint32_t period_us = 1000000 / frequency;
  float duty_percent = (float)pulse_width_us / (float)period_us * 100.0f;
  uint32_t max_duty = (1 << resolution) - 1;
  
  return (uint32_t)(duty_percent / 100.0f * max_duty);
}

// Calculate pulse width from duty cycle
uint32_t HAL_PWM_CalculatePulseWidth(uint32_t duty_cycle, uint32_t frequency, uint8_t resolution) {
  uint32_t max_duty = (1 << resolution) - 1;
  float duty_percent = (float)duty_cycle / (float)max_duty * 100.0f;
  uint32_t period_us = 1000000 / frequency;
  
  return (uint32_t)(duty_percent / 100.0f * period_us);
}

// Convert duty cycle to percentage
float HAL_PWM_DutyCycleToPercent(uint32_t duty_cycle, uint8_t resolution) {
  uint32_t max_duty = (1 << resolution) - 1;
  return (float)duty_cycle / (float)max_duty * 100.0f;
}

// Convert percentage to duty cycle
uint32_t HAL_PWM_PercentToDutyCycle(float percent, uint8_t resolution) {
  if (percent < 0.0f) percent = 0.0f;
  if (percent > 100.0f) percent = 100.0f;
  
  uint32_t max_duty = (1 << resolution) - 1;
  return (uint32_t)(percent / 100.0f * max_duty);
}

// Check if channel is available
bool HAL_PWM_IsChannelAvailable(uint8_t channel) {
  if (!pwm_initialized || channel >= 16) {
    return false;
  }
  
  return !pwm_channels[channel].enabled;
}

// Get available channel
uint8_t HAL_PWM_GetAvailableChannel(void) {
  if (!pwm_initialized) {
    return 255;
  }
  
  for (uint8_t i = 0; i < 16; i++) {
    if (!pwm_channels[i].enabled) {
      return i;
    }
  }
  
  return 255; // No available channels
}

// Get maximum channels
uint8_t HAL_PWM_GetMaxChannels(void) {
  return 16;
}

// Validate frequency
bool HAL_PWM_IsValidFrequency(uint32_t frequency) {
  return (frequency >= 1 && frequency <= 40000000); // 1Hz to 40MHz
}

// Validate resolution
bool HAL_PWM_IsValidResolution(uint8_t resolution) {
  return (resolution >= 1 && resolution <= 20); // 1 to 20 bits
}

// Validate duty cycle
bool HAL_PWM_IsValidDutyCycle(uint32_t duty_cycle, uint8_t resolution) {
  uint32_t max_duty = (1 << resolution) - 1;
  return (duty_cycle <= max_duty);
}
