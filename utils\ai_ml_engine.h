/*
 * QuadFly Flight Controller - AI/ML Engine Header
 * 
 * Machine learning for flight optimization and predictive maintenance
 */

#ifndef AI_ML_ENGINE_H
#define AI_ML_ENGINE_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include "math_utils.h"

// ML model types
typedef enum {
  ML_MODEL_LINEAR_REGRESSION = 0,
  ML_MODEL_NEURAL_NETWORK = 1,
  ML_<PERSON><PERSON><PERSON>_DECISION_TREE = 2,
  ML_MODEL_KALMAN_FILTER = 3,
  ML_MODEL_FUZZY_LOGIC = 4,
  ML_MODEL_ADAPTIVE_CONTROL = 5
} MLModelType_t;

// Learning algorithms
typedef enum {
  LEARNING_SUPERVISED = 0,
  LEARNING_UNSUPERVISED = 1,
  LEARNING_REINFORCEMENT = 2,
  LEARNING_ONLINE = 3,
  LEARNING_BATCH = 4
} LearningType_t;

// Flight characteristics data
typedef struct {
  // Input features
  float throttle_input;
  float roll_input;
  float pitch_input;
  float yaw_input;
  
  // Environmental conditions
  float wind_speed;
  float wind_direction;
  float air_density;
  float temperature;
  float humidity;
  
  // Vehicle state
  float battery_voltage;
  float total_weight;
  float center_of_gravity[3];
  float moment_of_inertia[3];
  
  // Performance metrics
  float response_time;
  float settling_time;
  float overshoot;
  float steady_state_error;
  float power_consumption;
  
  // Timestamp and validity
  uint32_t timestamp;
  bool valid;
} FlightCharacteristics_t;

// Predictive maintenance data
typedef struct {
  // Component health indicators
  float motor_vibration[4];
  float motor_temperature[4];
  float motor_current[4];
  float motor_efficiency[4];
  
  float esc_temperature[4];
  float esc_efficiency[4];
  
  float battery_health;
  float battery_capacity_remaining;
  float battery_internal_resistance;
  
  float sensor_noise_level[5];
  float sensor_drift_rate[5];
  float sensor_response_time[5];
  
  // Usage statistics
  uint32_t flight_hours;
  uint32_t motor_runtime[4];
  uint32_t hard_landings;
  uint32_t emergency_stops;
  uint32_t temperature_cycles;
  
  // Predicted maintenance
  uint32_t days_until_maintenance;
  float failure_probability;
  uint8_t critical_components;
  
} PredictiveMaintenanceData_t;

// Neural network structure
typedef struct {
  uint8_t input_size;
  uint8_t hidden_layers;
  uint8_t hidden_size;
  uint8_t output_size;
  
  float* weights_input_hidden;
  float* weights_hidden_output;
  float* bias_hidden;
  float* bias_output;
  
  float* hidden_activations;
  float* output_activations;
  
  float learning_rate;
  uint32_t training_epochs;
  bool trained;
  
} NeuralNetwork_t;

// Adaptive PID controller
typedef struct {
  float kp, ki, kd;
  float kp_min, kp_max;
  float ki_min, ki_max;
  float kd_min, kd_max;
  
  float adaptation_rate;
  float performance_threshold;
  
  float error_history[10];
  float output_history[10];
  uint8_t history_index;
  
  bool adaptation_enabled;
  uint32_t last_adaptation;
  
} AdaptivePID_t;

// Fuzzy logic controller
typedef struct {
  uint8_t input_count;
  uint8_t rule_count;
  uint8_t membership_functions;
  
  float* input_ranges;
  float* output_ranges;
  float* membership_values;
  float* rule_weights;
  
  bool initialized;
  
} FuzzyController_t;

// ML training data
typedef struct {
  float* inputs;
  float* outputs;
  uint16_t sample_count;
  uint16_t input_size;
  uint16_t output_size;
  uint32_t timestamp;
  bool normalized;
} TrainingData_t;

// Function declarations

// Initialization and configuration
bool AIML_Init(void);
void AIML_DeInit(void);
bool AIML_EnableFeature(const char* feature_name, bool enable);
void AIML_SetLearningRate(float rate);
void AIML_SetTrainingMode(bool enable);

// Flight characteristics optimization
bool AIML_InitFlightOptimization(void);
void AIML_UpdateFlightData(const FlightCharacteristics_t* data);
bool AIML_OptimizePIDGains(float* kp, float* ki, float* kd);
bool AIML_OptimizeMotorMixing(float* motor_mix_matrix);
float AIML_PredictOptimalThrottle(float desired_altitude, float current_altitude);
Vector3_t AIML_PredictOptimalAttitude(Vector3_t target_position, Vector3_t current_position);

// Adaptive control
bool AIML_InitAdaptiveControl(void);
void AIML_UpdateAdaptivePID(AdaptivePID_t* pid, float error, float output, float dt);
bool AIML_AdaptToWindConditions(float wind_speed, float wind_direction);
bool AIML_AdaptToPayloadChange(float weight_change, Vector3_t cg_shift);
void AIML_LearnFromFlight(const FlightCharacteristics_t* flight_data);

// Predictive maintenance
bool AIML_InitPredictiveMaintenance(void);
void AIML_UpdateMaintenanceData(const PredictiveMaintenanceData_t* data);
uint32_t AIML_PredictMaintenanceInterval(uint8_t component_id);
float AIML_PredictFailureProbability(uint8_t component_id, uint32_t time_horizon);
bool AIML_DetectAnomalies(const PredictiveMaintenanceData_t* data);
void AIML_GenerateMaintenanceAlert(uint8_t component_id, uint8_t severity);

// Neural network functions
bool AIML_CreateNeuralNetwork(NeuralNetwork_t* nn, uint8_t input_size, uint8_t hidden_size, uint8_t output_size);
void AIML_DestroyNeuralNetwork(NeuralNetwork_t* nn);
bool AIML_TrainNeuralNetwork(NeuralNetwork_t* nn, const TrainingData_t* training_data);
bool AIML_PredictNeuralNetwork(const NeuralNetwork_t* nn, const float* inputs, float* outputs);
float AIML_EvaluateNeuralNetwork(const NeuralNetwork_t* nn, const TrainingData_t* test_data);

// Fuzzy logic functions
bool AIML_CreateFuzzyController(FuzzyController_t* fuzzy, uint8_t input_count, uint8_t rule_count);
void AIML_DestroyFuzzyController(FuzzyController_t* fuzzy);
bool AIML_AddFuzzyRule(FuzzyController_t* fuzzy, const float* inputs, float output, float weight);
float AIML_EvaluateFuzzyController(const FuzzyController_t* fuzzy, const float* inputs);

// Online learning
void AIML_EnableOnlineLearning(bool enable);
void AIML_AddTrainingSample(const float* inputs, const float* outputs);
void AIML_UpdateModelOnline(void);
bool AIML_IsModelConverged(void);

// Feature extraction
void AIML_ExtractFlightFeatures(const FlightCharacteristics_t* data, float* features);
void AIML_ExtractMaintenanceFeatures(const PredictiveMaintenanceData_t* data, float* features);
void AIML_NormalizeFeatures(float* features, uint8_t count);
void AIML_DenormalizeFeatures(float* features, uint8_t count);

// Model evaluation and validation
float AIML_CalculateModelAccuracy(MLModelType_t model_type);
float AIML_CalculatePredictionError(MLModelType_t model_type);
bool AIML_ValidateModel(MLModelType_t model_type, const TrainingData_t* validation_data);
void AIML_CrossValidateModel(MLModelType_t model_type, uint8_t folds);

// Data management
bool AIML_SaveTrainingData(const char* filename);
bool AIML_LoadTrainingData(const char* filename);
void AIML_ClearTrainingData(void);
uint16_t AIML_GetTrainingDataSize(void);

// Model persistence
bool AIML_SaveModel(MLModelType_t model_type, const char* filename);
bool AIML_LoadModel(MLModelType_t model_type, const char* filename);
void AIML_ResetModel(MLModelType_t model_type);

// Performance monitoring
typedef struct {
  uint32_t predictions_made;
  uint32_t training_iterations;
  float average_prediction_time_us;
  float average_training_time_ms;
  float model_accuracy;
  float prediction_confidence;
  uint32_t last_update;
} AIMLPerformance_t;

AIMLPerformance_t AIML_GetPerformanceMetrics(void);
void AIML_ResetPerformanceMetrics(void);

// Advanced features
bool AIML_EnableTransferLearning(bool enable);
bool AIML_LoadPretrainedModel(const char* model_name);
void AIML_EnableEnsembleLearning(bool enable);
void AIML_SetEnsembleWeights(const float* weights, uint8_t count);

// Real-time optimization
void AIML_EnableRealTimeOptimization(bool enable);
void AIML_SetOptimizationInterval(uint32_t interval_ms);
bool AIML_OptimizeInRealTime(void);

// Safety and constraints
void AIML_SetSafetyConstraints(const float* min_values, const float* max_values, uint8_t count);
bool AIML_ValidateOutputSafety(const float* outputs, uint8_t count);
void AIML_EnableSafetyOverride(bool enable);

// Debugging and diagnostics
void AIML_PrintModelInfo(MLModelType_t model_type);
void AIML_PrintTrainingProgress(void);
void AIML_PrintPredictionResults(void);
void AIML_LogMLActivity(void);

// Configuration
typedef struct {
  bool enable_flight_optimization;
  bool enable_predictive_maintenance;
  bool enable_adaptive_control;
  bool enable_online_learning;
  float learning_rate;
  uint16_t max_training_samples;
  uint32_t training_interval_ms;
  uint8_t neural_network_hidden_size;
  uint8_t fuzzy_membership_functions;
} AIMLConfig_t;

bool AIML_Configure(const AIMLConfig_t* config);
AIMLConfig_t AIML_GetConfiguration(void);

// Error handling
typedef enum {
  AIML_ERROR_NONE = 0,
  AIML_ERROR_INVALID_MODEL,
  AIML_ERROR_INSUFFICIENT_DATA,
  AIML_ERROR_TRAINING_FAILED,
  AIML_ERROR_PREDICTION_FAILED,
  AIML_ERROR_MODEL_NOT_TRAINED,
  AIML_ERROR_INVALID_INPUT,
  AIML_ERROR_MEMORY_ALLOCATION,
  AIML_ERROR_FILE_IO,
  AIML_ERROR_SAFETY_VIOLATION
} AIMLError_t;

AIMLError_t AIML_GetLastError(void);
const char* AIML_GetErrorString(AIMLError_t error);

#endif // AI_ML_ENGINE_H
