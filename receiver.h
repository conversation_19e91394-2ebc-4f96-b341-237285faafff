/*
 * QuadFly Flight Controller - Receiver Handler Header
 * 
 * FlySky FS-TH9X radio receiver interface
 */

#ifndef RECEIVER_H
#define RECEIVER_H

#include "config.h"

// Function declarations
bool initializeReceiver(void);
void readReceiverInputs(ReceiverData_t *receiverData);
uint16_t getPWMChannel(uint8_t channel);
void checkSignalQuality(ReceiverData_t *receiverData);
void calibrateReceiver(void);

// PWM interrupt handlers
void IRAM_ATTR throttleInterrupt(void);
void IRAM_ATTR rollInterrupt(void);
void IRAM_ATTR pitchInterrupt(void);
void IRAM_ATTR yawInterrupt(void);
void IRAM_ATTR modeInterrupt(void);
void IRAM_ATTR aux1Interrupt(void);
void IRAM_ATTR aux2Interrupt(void);
void IRAM_ATTR handlePWMInterrupt(uint8_t channel);

#endif // RECEIVER_H