/*
 * QuadFly Flight Controller - Enhanced Configuration Manager Implementation
 * 
 * Advanced configuration management with versioning, validation, and backup
 */

#include "config_manager.h"
#include <EEPROM.h>
#include <ArduinoJson.h>
#include <esp_crc.h>

// Global configuration instance
EnhancedConfig_t g_config;

// Configuration storage layout
#define CONFIG_BASE_ADDRESS 0
#define CONFIG_BACKUP_ADDRESS 1024
#define CONFIG_BACKUP_SIZE 512
#define CONFIG_CHANGE_LOG_ADDRESS 2048
#define CONFIG_CHANGE_LOG_SIZE 256

// Internal state
static ConfigError_t last_error = CONFIG_ERROR_NONE;
static bool config_locked = false;
static char config_password[32] = "";
static ConfigChange_t change_history[16];
static uint8_t change_count = 0;

// Default configuration values
static const EnhancedConfig_t default_config = {
  .header = {
    .signature = CONFIG_SIGNATURE,
    .version = CONFIG_MANAGER_VERSION,
    .size = sizeof(EnhancedConfig_t),
    .timestamp = 0,
    .checksum = 0,
    .validation_magic = CONFIG_VALIDATION_MAGIC,
    .backup_count = 0,
    .reserved = {0}
  },
  
  // Default PID parameters
  .rollPID = {1.0f, 0.1f, 0.05f, 100.0f, 400.0f},
  .pitchPID = {1.0f, 0.1f, 0.05f, 100.0f, 400.0f},
  .yawPID = {2.0f, 0.2f, 0.0f, 100.0f, 400.0f},
  .altPID = {3.0f, 0.5f, 1.0f, 100.0f, 400.0f},
  .velPID = {2.0f, 0.3f, 0.1f, 100.0f, 400.0f},
  .posPID = {1.5f, 0.2f, 0.05f, 100.0f, 400.0f},
  
  // Default RC values
  .rcMin = {1000, 1000, 1000, 1000, 1000, 1000, 1000, 1000},
  .rcMax = {2000, 2000, 2000, 2000, 2000, 2000, 2000, 2000},
  .rcMid = {1500, 1500, 1500, 1500, 1500, 1500, 1500, 1500},
  .rcDeadband = 10,
  .rcFailsafeValues = {1500, 1500, 1000, 1500, 1000, 1000, 1000, 1000},
  
  // Default safety parameters
  .maxTiltAngle = 45.0f,
  .maxVerticalSpeed = 5.0f,
  .maxHorizontalSpeed = 10.0f,
  .failsafeThrottle = 1000,
  .lowVoltageThreshold = 3300,
  .criticalVoltageThreshold = 3000,
  .geofenceRadius = 100,
  .geofenceHeight = 50.0f,
  
  // Default flight parameters
  .motorIdleSpeed = 5,
  .motorMaxSpeed = 100,
  .enableGPS = true,
  .enableBarometer = true,
  .enableMagnetometer = true,
  .enableTelemetry = true,
  .enableDataLogging = true,
  
  // Default calibration (all zeros)
  .accelOffset = {0.0f, 0.0f, 0.0f},
  .accelScale = {1.0f, 1.0f, 1.0f},
  .gyroOffset = {0.0f, 0.0f, 0.0f},
  .magOffset = {0.0f, 0.0f, 0.0f},
  .magScale = {1.0f, 1.0f, 1.0f},
  .baroOffset = 0.0f,
  
  // Default advanced features
  .enableAI = false,
  .enablePredictiveMaintenance = false,
  .sensorFusionMode = 1,
  .flightModeDefault = 0,
  
  // Default system settings
  .logLevel = 2,
  .telemetryRate = 50,
  .sensorRate = 200,
  .controlRate = 100,
  
  // User parameters (all zeros)
  .userParams = {0},
  .userParamNames = {{0}}
};

// Parameter metadata table
static const ConfigParameter_t parameter_table[] = {
  {0, "roll_p", "Roll P gain", 0.0f, 10.0f, 1.0f, 0, false},
  {1, "roll_i", "Roll I gain", 0.0f, 5.0f, 0.1f, 0, false},
  {2, "roll_d", "Roll D gain", 0.0f, 1.0f, 0.05f, 0, false},
  {3, "pitch_p", "Pitch P gain", 0.0f, 10.0f, 1.0f, 0, false},
  {4, "pitch_i", "Pitch I gain", 0.0f, 5.0f, 0.1f, 0, false},
  {5, "pitch_d", "Pitch D gain", 0.0f, 1.0f, 0.05f, 0, false},
  {6, "yaw_p", "Yaw P gain", 0.0f, 10.0f, 2.0f, 0, false},
  {7, "yaw_i", "Yaw I gain", 0.0f, 5.0f, 0.2f, 0, false},
  {8, "yaw_d", "Yaw D gain", 0.0f, 1.0f, 0.0f, 0, false},
  {9, "max_tilt", "Maximum tilt angle", 10.0f, 60.0f, 45.0f, 0, false},
  {10, "max_vert_speed", "Maximum vertical speed", 1.0f, 20.0f, 5.0f, 0, false},
  // Add more parameters as needed...
};

#define PARAMETER_COUNT (sizeof(parameter_table) / sizeof(ConfigParameter_t))

// Internal helper functions
static void set_error(ConfigError_t error) {
  last_error = error;
}

static uint32_t calculate_crc32(const void* data, size_t length) {
  return esp_crc32_le(0, (const uint8_t*)data, length);
}

// Initialize configuration manager
bool ConfigManager_Init(void) {
  Serial.println("Initializing Enhanced Configuration Manager...");
  
  // Initialize EEPROM
  if (!EEPROM.begin(4096)) {
    set_error(CONFIG_ERROR_UNKNOWN);
    return false;
  }
  
  // Load configuration
  if (!ConfigManager_Load()) {
    Serial.println("Failed to load configuration, using defaults");
    g_config = default_config;
    g_config.header.timestamp = millis();
    ConfigManager_Save();
  }
  
  Serial.println("Configuration Manager initialized successfully");
  return true;
}

// Load configuration from storage
bool ConfigManager_Load(void) {
  Serial.println("Loading configuration...");
  
  // Read configuration header first
  ConfigHeader_t header;
  EEPROM.get(CONFIG_BASE_ADDRESS, header);
  
  // Validate header
  if (header.signature != CONFIG_SIGNATURE) {
    Serial.println("Invalid configuration signature");
    set_error(CONFIG_ERROR_CORRUPTED);
    return false;
  }
  
  if (header.size != sizeof(EnhancedConfig_t)) {
    Serial.println("Configuration size mismatch");
    set_error(CONFIG_ERROR_CORRUPTED);
    return false;
  }
  
  // Read full configuration
  EEPROM.get(CONFIG_BASE_ADDRESS, g_config);
  
  // Validate configuration
  ConfigValidationResult_t result = ConfigManager_Validate(&g_config);
  if (result != CONFIG_VALID) {
    Serial.printf("Configuration validation failed: %d\n", result);
    set_error(CONFIG_ERROR_CORRUPTED);
    return false;
  }
  
  Serial.println("Configuration loaded successfully");
  return true;
}

// Save configuration to storage
bool ConfigManager_Save(void) {
  if (config_locked) {
    set_error(CONFIG_ERROR_LOCKED);
    return false;
  }
  
  Serial.println("Saving configuration...");
  
  // Update header
  g_config.header.timestamp = millis();
  g_config.header.checksum = calculate_crc32(&g_config, sizeof(EnhancedConfig_t) - sizeof(uint32_t));
  
  // Write to EEPROM
  EEPROM.put(CONFIG_BASE_ADDRESS, g_config);
  if (!EEPROM.commit()) {
    set_error(CONFIG_ERROR_UNKNOWN);
    return false;
  }
  
  Serial.println("Configuration saved successfully");
  return true;
}

// Reset configuration to defaults
bool ConfigManager_Reset(void) {
  if (config_locked) {
    set_error(CONFIG_ERROR_LOCKED);
    return false;
  }
  
  Serial.println("Resetting configuration to defaults...");
  
  g_config = default_config;
  g_config.header.timestamp = millis();
  
  return ConfigManager_Save();
}

// Validate configuration
ConfigValidationResult_t ConfigManager_Validate(const EnhancedConfig_t* config) {
  if (config == NULL) {
    return CONFIG_CORRUPTED;
  }
  
  // Check signature
  if (config->header.signature != CONFIG_SIGNATURE) {
    return CONFIG_INVALID_SIGNATURE;
  }
  
  // Check version
  if (!ConfigManager_IsVersionSupported(config->header.version)) {
    return CONFIG_INVALID_VERSION;
  }
  
  // Check validation magic
  if (config->header.validation_magic != CONFIG_VALIDATION_MAGIC) {
    return CONFIG_CORRUPTED;
  }
  
  // Calculate and verify checksum
  uint32_t calculated_checksum = calculate_crc32(config, sizeof(EnhancedConfig_t) - sizeof(uint32_t));
  if (calculated_checksum != config->header.checksum) {
    return CONFIG_INVALID_CHECKSUM;
  }
  
  // Validate parameter ranges
  if (!ConfigManager_ValidateAllParameters()) {
    return CONFIG_INVALID_PARAMETERS;
  }
  
  return CONFIG_VALID;
}

// Check if version is supported
bool ConfigManager_IsVersionSupported(uint16_t version) {
  return (version >= 1 && version <= CONFIG_MANAGER_VERSION);
}

// Get current version
uint16_t ConfigManager_GetCurrentVersion(void) {
  return CONFIG_MANAGER_VERSION;
}

// Create backup
ConfigBackupStatus_t ConfigManager_CreateBackup(const char* name) {
  if (config_locked) {
    return BACKUP_FAILED;
  }
  
  // Implementation would store backup with timestamp and name
  // For now, just increment backup count
  if (g_config.header.backup_count < MAX_CONFIG_BACKUPS) {
    g_config.header.backup_count++;
    ConfigManager_Save();
    return BACKUP_SUCCESS;
  }
  
  return BACKUP_FULL;
}

// Get parameter by ID
bool ConfigManager_GetParameter(uint16_t param_id, void* value) {
  if (param_id >= PARAMETER_COUNT || value == NULL) {
    set_error(CONFIG_ERROR_INVALID_PARAM);
    return false;
  }
  
  // Map parameter ID to actual config field
  switch (param_id) {
    case 0: *(float*)value = g_config.rollPID.kp; break;
    case 1: *(float*)value = g_config.rollPID.ki; break;
    case 2: *(float*)value = g_config.rollPID.kd; break;
    case 3: *(float*)value = g_config.pitchPID.kp; break;
    case 4: *(float*)value = g_config.pitchPID.ki; break;
    case 5: *(float*)value = g_config.pitchPID.kd; break;
    case 6: *(float*)value = g_config.yawPID.kp; break;
    case 7: *(float*)value = g_config.yawPID.ki; break;
    case 8: *(float*)value = g_config.yawPID.kd; break;
    case 9: *(float*)value = g_config.maxTiltAngle; break;
    case 10: *(float*)value = g_config.maxVerticalSpeed; break;
    default:
      set_error(CONFIG_ERROR_INVALID_PARAM);
      return false;
  }
  
  return true;
}

// Set parameter by ID
bool ConfigManager_SetParameter(uint16_t param_id, const void* value) {
  if (config_locked) {
    set_error(CONFIG_ERROR_LOCKED);
    return false;
  }
  
  if (param_id >= PARAMETER_COUNT || value == NULL) {
    set_error(CONFIG_ERROR_INVALID_PARAM);
    return false;
  }
  
  // Validate parameter value
  if (!ConfigManager_ValidateParameter(param_id, value)) {
    set_error(CONFIG_ERROR_OUT_OF_RANGE);
    return false;
  }
  
  // Get old value for change tracking
  float old_value;
  ConfigManager_GetParameter(param_id, &old_value);
  
  // Set new value
  float new_value = *(const float*)value;
  switch (param_id) {
    case 0: g_config.rollPID.kp = new_value; break;
    case 1: g_config.rollPID.ki = new_value; break;
    case 2: g_config.rollPID.kd = new_value; break;
    case 3: g_config.pitchPID.kp = new_value; break;
    case 4: g_config.pitchPID.ki = new_value; break;
    case 5: g_config.pitchPID.kd = new_value; break;
    case 6: g_config.yawPID.kp = new_value; break;
    case 7: g_config.yawPID.ki = new_value; break;
    case 8: g_config.yawPID.kd = new_value; break;
    case 9: g_config.maxTiltAngle = new_value; break;
    case 10: g_config.maxVerticalSpeed = new_value; break;
    default:
      set_error(CONFIG_ERROR_INVALID_PARAM);
      return false;
  }
  
  // Log change
  ConfigManager_LogChange(param_id, old_value, new_value);
  
  return true;
}

// Validate parameter value
bool ConfigManager_ValidateParameter(uint16_t param_id, const void* value) {
  if (param_id >= PARAMETER_COUNT || value == NULL) {
    return false;
  }
  
  const ConfigParameter_t* param = &parameter_table[param_id];
  float val = *(const float*)value;
  
  return (val >= param->min_value && val <= param->max_value);
}

// Validate all parameters
bool ConfigManager_ValidateAllParameters(void) {
  // Check critical parameters
  if (g_config.maxTiltAngle < 10.0f || g_config.maxTiltAngle > 60.0f) return false;
  if (g_config.maxVerticalSpeed < 1.0f || g_config.maxVerticalSpeed > 20.0f) return false;
  if (g_config.motorIdleSpeed > g_config.motorMaxSpeed) return false;
  
  // Add more validation as needed
  return true;
}

// Calculate checksum
uint32_t ConfigManager_CalculateChecksum(const EnhancedConfig_t* config) {
  return calculate_crc32(config, sizeof(EnhancedConfig_t) - sizeof(uint32_t));
}

// Log configuration change
bool ConfigManager_LogChange(uint16_t param_id, float old_value, float new_value) {
  if (change_count >= 16) {
    // Shift array to make room
    for (int i = 0; i < 15; i++) {
      change_history[i] = change_history[i + 1];
    }
    change_count = 15;
  }
  
  ConfigChange_t* change = &change_history[change_count++];
  change->timestamp = millis();
  change->parameter_id = param_id;
  change->old_value = old_value;
  change->new_value = new_value;
  
  if (param_id < PARAMETER_COUNT) {
    strncpy(change->description, parameter_table[param_id].name, 31);
    change->description[31] = '\0';
  } else {
    strcpy(change->description, "Unknown");
  }
  
  return true;
}

// Get last error
ConfigError_t ConfigManager_GetLastError(void) {
  return last_error;
}

// Get error string
const char* ConfigManager_GetErrorString(ConfigError_t error) {
  switch (error) {
    case CONFIG_ERROR_NONE: return "No error";
    case CONFIG_ERROR_INVALID_PARAM: return "Invalid parameter";
    case CONFIG_ERROR_OUT_OF_RANGE: return "Value out of range";
    case CONFIG_ERROR_READ_ONLY: return "Parameter is read-only";
    case CONFIG_ERROR_STORAGE_FULL: return "Storage full";
    case CONFIG_ERROR_CORRUPTED: return "Configuration corrupted";
    case CONFIG_ERROR_LOCKED: return "Configuration locked";
    default: return "Unknown error";
  }
}
