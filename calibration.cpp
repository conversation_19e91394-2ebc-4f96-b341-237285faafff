/*
 * QuadFly Flight Controller - Calibration System Implementation
 * 
 * Professional-grade calibration routines for all sensors
 */

#include "calibration.h"

// Perform complete system calibration
void performSystemCalibration(void) {
  Serial.println("=== QuadFly System Calibration ===");
  Serial.println("WARNING: Ensure aircraft is on level surface and propellers are removed!");
  Serial.println("Press any key to continue...");
  
  while (!Serial.available()) {
    delay(100);
  }
  Serial.read(); // Clear input
  
  SensorData_t tempSensorData;
  memset(&tempSensorData, 0, sizeof(SensorData_t));
  
  // Calibrate gyroscope first (most critical)
  calibrateGyroscope(&tempSensorData);
  delay(1000);
  
  // Calibrate accelerometer
  calibrateAccelerometer(&tempSensorData);
  delay(1000);
  
  // Calibrate magnetometer
  calibrateMagnetometer(&tempSensorData);
  delay(1000);
  
  // Calibrate barometer
  calibrateBarometer(&tempSensorData);
  delay(1000);
  
  // Save calibration data
  saveCalibrationData(&tempSensorData);
  
  Serial.println("=== Calibration Complete ===");
  Serial.println("System is now ready for flight operations");
}

// Calibrate gyroscope (bias removal)
void calibrateGyroscope(SensorData_t *sensorData) {
  Serial.println("Calibrating gyroscope...");
  Serial.println("Keep aircraft stationary for 10 seconds");
  
  float gyroSum[3] = {0, 0, 0};
  int samples = 0;
  uint32_t startTime = millis();
  
  while (millis() - startTime < 10000) { // 10 seconds
    // Read raw gyro data
    Wire.beginTransmission(MPU6050_ADDRESS);
    Wire.write(0x43); // GYRO_XOUT_H
    Wire.endTransmission();
    Wire.requestFrom(MPU6050_ADDRESS, 6);
    
    if (Wire.available() >= 6) {
      int16_t gyroX_raw = (Wire.read() << 8) | Wire.read();
      int16_t gyroY_raw = (Wire.read() << 8) | Wire.read();
      int16_t gyroZ_raw = (Wire.read() << 8) | Wire.read();
      
      // Convert to physical units
      gyroSum[0] += ((float)gyroX_raw / 32.8f) * DEG_TO_RAD;
      gyroSum[1] += ((float)gyroY_raw / 32.8f) * DEG_TO_RAD;
      gyroSum[2] += ((float)gyroZ_raw / 32.8f) * DEG_TO_RAD;
      
      samples++;
    }
    
    // Progress indication
    if ((millis() - startTime) % 1000 == 0) {
      Serial.print(".");
    }
    
    delay(10);
  }
  
  // Calculate offsets
  if (samples > 0) {
    sensorData->gyroOffset[0] = gyroSum[0] / samples;
    sensorData->gyroOffset[1] = gyroSum[1] / samples;
    sensorData->gyroOffset[2] = gyroSum[2] / samples;
    
    Serial.println("\nGyroscope calibration complete");
    Serial.print("X offset: "); Serial.println(sensorData->gyroOffset[0] * RAD_TO_DEG);
    Serial.print("Y offset: "); Serial.println(sensorData->gyroOffset[1] * RAD_TO_DEG);
    Serial.print("Z offset: "); Serial.println(sensorData->gyroOffset[2] * RAD_TO_DEG);
  } else {
    Serial.println("\nGyroscope calibration FAILED - no data received");
  }
}

// Calibrate accelerometer (bias and scale)
void calibrateAccelerometer(SensorData_t *sensorData) {
  Serial.println("Calibrating accelerometer...");
  Serial.println("Place aircraft in 6 orientations as prompted:");
  Serial.println("1. Level (Z-up)");
  Serial.println("2. Inverted (Z-down)");
  Serial.println("3. Left side (Y-up)");
  Serial.println("4. Right side (Y-down)");
  Serial.println("5. Nose up (X-up)");
  Serial.println("6. Nose down (X-down)");
  
  float accelReadings[6][3]; // 6 orientations, 3 axes
  const char* orientations[] = {"Level", "Inverted", "Left side", "Right side", "Nose up", "Nose down"};
  
  for (int orientation = 0; orientation < 6; orientation++) {
    Serial.print("Position ");
    Serial.print(orientation + 1);
    Serial.print(" (");
    Serial.print(orientations[orientation]);
    Serial.println("): Press any key when ready...");
    
    while (!Serial.available()) {
      delay(100);
    }
    Serial.read(); // Clear input
    
    Serial.println("Measuring... keep steady");
    
    float accelSum[3] = {0, 0, 0};
    int samples = 0;
    
    for (int i = 0; i < 200; i++) { // 2 seconds at 100Hz
      Wire.beginTransmission(MPU6050_ADDRESS);
      Wire.write(0x3B); // ACCEL_XOUT_H
      Wire.endTransmission();
      Wire.requestFrom(MPU6050_ADDRESS, 6);
      
      if (Wire.available() >= 6) {
        int16_t accelX_raw = (Wire.read() << 8) | Wire.read();
        int16_t accelY_raw = (Wire.read() << 8) | Wire.read();
        int16_t accelZ_raw = (Wire.read() << 8) | Wire.read();
        
        accelSum[0] += ((float)accelX_raw / 4096.0f) * 9.81f;
        accelSum[1] += ((float)accelY_raw / 4096.0f) * 9.81f;
        accelSum[2] += ((float)accelZ_raw / 4096.0f) * 9.81f;
        
        samples++;
      }
      
      delay(10);
    }
    
    if (samples > 0) {
      accelReadings[orientation][0] = accelSum[0] / samples;
      accelReadings[orientation][1] = accelSum[1] / samples;
      accelReadings[orientation][2] = accelSum[2] / samples;
      
      Serial.println("Position complete");
    }
  }
  
  // Calculate offsets (simplified 6-point calibration)
  sensorData->accelOffset[0] = (accelReadings[4][0] + accelReadings[5][0]) / 2.0f;
  sensorData->accelOffset[1] = (accelReadings[2][1] + accelReadings[3][1]) / 2.0f;
  sensorData->accelOffset[2] = (accelReadings[0][2] + accelReadings[1][2]) / 2.0f;
  
  Serial.println("Accelerometer calibration complete");
  Serial.print("X offset: "); Serial.println(sensorData->accelOffset[0]);
  Serial.print("Y offset: "); Serial.println(sensorData->accelOffset[1]);
  Serial.print("Z offset: "); Serial.println(sensorData->accelOffset[2]);
}

// Calibrate magnetometer (hard and soft iron compensation)
void calibrateMagnetometer(SensorData_t *sensorData) {
  Serial.println("Calibrating magnetometer...");
  Serial.println("Rotate aircraft slowly in all directions for 30 seconds");
  Serial.println("Try to cover all orientations evenly");
  Serial.println("Press any key to start...");
  
  while (!Serial.available()) {
    delay(100);
  }
  Serial.read(); // Clear input
  
  float magMin[3] = {32767, 32767, 32767};
  float magMax[3] = {-32768, -32768, -32768};
  
  uint32_t startTime = millis();
  int samples = 0;
  
  while (millis() - startTime < 30000) { // 30 seconds
    Wire.beginTransmission(HMC5883L_ADDRESS);
    Wire.write(0x03); // DXRA
    Wire.endTransmission();
    Wire.requestFrom(HMC5883L_ADDRESS, 6);
    
    if (Wire.available() >= 6) {
      int16_t magX_raw = (Wire.read() << 8) | Wire.read();
      int16_t magZ_raw = (Wire.read() << 8) | Wire.read(); // Note: Z before Y
      int16_t magY_raw = (Wire.read() << 8) | Wire.read();
      
      // Update min/max values
      magMin[0] = min(magMin[0], (float)magX_raw);
      magMax[0] = max(magMax[0], (float)magX_raw);
      magMin[1] = min(magMin[1], (float)magY_raw);
      magMax[1] = max(magMax[1], (float)magY_raw);
      magMin[2] = min(magMin[2], (float)magZ_raw);
      magMax[2] = max(magMax[2], (float)magZ_raw);
      
      samples++;
    }
    
    // Progress indication
    if ((millis() - startTime) % 3000 == 0) {
      Serial.print("Progress: ");
      Serial.print((millis() - startTime) / 300);
      Serial.println("%");
    }
    
    delay(50);
  }
  
  // Calculate hard iron offsets and soft iron scale factors
  for (int i = 0; i < 3; i++) {
    sensorData->magOffset[i] = (magMax[i] + magMin[i]) / 2.0f;
    sensorData->magScale[i] = 2.0f / (magMax[i] - magMin[i]);
  }
  
  Serial.println("Magnetometer calibration complete");
  Serial.print("X: offset="); Serial.print(sensorData->magOffset[0]); 
  Serial.print(", scale="); Serial.println(sensorData->magScale[0]);
  Serial.print("Y: offset="); Serial.print(sensorData->magOffset[1]); 
  Serial.print(", scale="); Serial.println(sensorData->magScale[1]);
  Serial.print("Z: offset="); Serial.print(sensorData->magOffset[2]); 
  Serial.print(", scale="); Serial.println(sensorData->magScale[2]);
  
  Serial.print("Samples collected: "); Serial.println(samples);
}

// Calibrate barometer (set ground level reference)
void calibrateBarometer(SensorData_t *sensorData) {
  Serial.println("Calibrating barometer...");
  Serial.println("Measuring ground level pressure...");
  
  float pressureSum = 0;
  int samples = 0;
  
  for (int i = 0; i < 100; i++) { // 10 seconds at 10Hz
    // Trigger pressure conversion
    Wire.beginTransmission(MS5611_ADDRESS);
    Wire.write(0x48); // Convert D1 (pressure) OSR=4096
    Wire.endTransmission();
    
    delay(10); // Wait for conversion
    
    // Read pressure result
    Wire.beginTransmission(MS5611_ADDRESS);
    Wire.write(0x00); // ADC Read
    Wire.endTransmission();
    Wire.requestFrom(MS5611_ADDRESS, 3);
    
    if (Wire.available() >= 3) {
      uint32_t D1 = ((uint32_t)Wire.read() << 16) | 
                    ((uint32_t)Wire.read() << 8) | 
                    Wire.read();
      
      // Simplified pressure calculation (would need calibration coefficients)
      float pressure = (float)D1;
      pressureSum += pressure;
      samples++;
    }
    
    delay(100);
  }
  
  if (samples > 0) {
    // Store reference pressure for altitude calculations
    float referencePressure = pressureSum / samples;
    Serial.print("Barometer calibration complete. Reference pressure: ");
    Serial.println(referencePressure);
  } else {
    Serial.println("Barometer calibration FAILED");
  }
}

// Save calibration data to EEPROM
void saveCalibrationData(SensorData_t *sensorData) {
  Serial.println("Saving calibration data...");
  
  EEPROM.begin(512);
  
  // Save gyro offsets
  EEPROM.put(100, sensorData->gyroOffset[0]);
  EEPROM.put(104, sensorData->gyroOffset[1]);
  EEPROM.put(108, sensorData->gyroOffset[2]);
  
  // Save accel offsets
  EEPROM.put(112, sensorData->accelOffset[0]);
  EEPROM.put(116, sensorData->accelOffset[1]);
  EEPROM.put(120, sensorData->accelOffset[2]);
  
  // Save mag offsets and scales
  EEPROM.put(124, sensorData->magOffset[0]);
  EEPROM.put(128, sensorData->magOffset[1]);
  EEPROM.put(132, sensorData->magOffset[2]);
  EEPROM.put(136, sensorData->magScale[0]);
  EEPROM.put(140, sensorData->magScale[1]);
  EEPROM.put(144, sensorData->magScale[2]);
  
  // Set calibration flag
  EEPROM.put(148, true);
  
  EEPROM.commit();
  EEPROM.end();
  
  Serial.println("Calibration data saved to EEPROM");
}

// Load calibration data from EEPROM
void loadCalibrationData(SensorData_t *sensorData) {
  EEPROM.begin(512);
  
  // Check if calibration data exists
  bool calibrated;
  EEPROM.get(148, calibrated);
  
  if (calibrated) {
    Serial.println("Loading calibration data from EEPROM...");
    
    // Load gyro offsets
    EEPROM.get(100, sensorData->gyroOffset[0]);
    EEPROM.get(104, sensorData->gyroOffset[1]);
    EEPROM.get(108, sensorData->gyroOffset[2]);
    
    // Load accel offsets
    EEPROM.get(112, sensorData->accelOffset[0]);
    EEPROM.get(116, sensorData->accelOffset[1]);
    EEPROM.get(120, sensorData->accelOffset[2]);
    
    // Load mag offsets and scales
    EEPROM.get(124, sensorData->magOffset[0]);
    EEPROM.get(128, sensorData->magOffset[1]);
    EEPROM.get(132, sensorData->magOffset[2]);
    EEPROM.get(136, sensorData->magScale[0]);
    EEPROM.get(140, sensorData->magScale[1]);
    EEPROM.get(144, sensorData->magScale[2]);
    
    Serial.println("Calibration data loaded successfully");
  } else {
    Serial.println("No calibration data found - using defaults");
    
    // Set default values
    memset(sensorData->gyroOffset, 0, sizeof(sensorData->gyroOffset));
    memset(sensorData->accelOffset, 0, sizeof(sensorData->accelOffset));
    memset(sensorData->magOffset, 0, sizeof(sensorData->magOffset));
    sensorData->magScale[0] = 1.0f;
    sensorData->magScale[1] = 1.0f;
    sensorData->magScale[2] = 1.0f;
  }
  
  EEPROM.end();
}