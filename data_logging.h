/*
 * QuadFly Flight Controller - Data Logging Header
 * 
 * Flight data logging and black box functionality
 */

#ifndef DATA_LOGGING_H
#define DATA_LOGGING_H

#include "config.h"
#include <FS.h>
#include <SPIFFS.h>

// Log entry structure
typedef struct {
  uint32_t timestamp;
  float roll, pitch, yaw;
  float altitude;
  float batteryVoltage;
  uint8_t flightMode;
  bool armed;
  uint8_t gpsStatus;
} LogEntry_t;

// Function declarations
void initializeDataLogging(void);
void logFlightData(FlightData_t *flightData, SystemState_t *systemState);
void createNewLogFile(void);
void closeLogFile(void);
void downloadLogs(void);
void clearLogs(void);

#endif // DATA_LOGGING_H