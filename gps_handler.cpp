/*
 * QuadFly Flight Controller - GPS Handler Implementation
 * 
 * Professional GPS data processing and navigation calculations
 */

#include "gps_handler.h"

// Global variables for GPS parsing
static String nmeaBuffer = "";

// Initialize GPS module
void initializeGPS(void) {
  Serial.println("Initializing GPS...");
  
  // GPS is initialized in main setup via Serial1
  Serial.println("GPS initialization complete");
  Serial.println("Waiting for GPS fix...");
}

// Process incoming GPS data
void processGPSData(GPSData_t *gpsData) {
  while (Serial1.available()) {
    char c = Serial1.read();
    
    if (c == '\n') {
      // Process complete NMEA sentence
      if (parseNMEA(nmeaBuffer, gpsData)) {
        gpsData->lastUpdate = millis();
      }
      nmeaBuffer = "";
    } else if (c != '\r') {
      nmeaBuffer += c;
    }
  }
  
  // Check for GPS timeout
  if (millis() - gpsData->lastUpdate > 2000) {
    gpsData->fix = false;
    gpsData->satellites = 0;
  }
}

// Parse NMEA sentences
bool parseNMEA(String sentence, GPSData_t *gpsData) {
  if (sentence.startsWith("$GPGGA")) {
    // Parse GGA sentence (Global Positioning System Fix Data)
    int commaIndex[14];
    int commaCount = 0;
    
    // Find all comma positions
    for (int i = 0; i < sentence.length() && commaCount < 14; i++) {
      if (sentence.charAt(i) == ',') {
        commaIndex[commaCount] = i;
        commaCount++;
      }
    }
    
    if (commaCount >= 13) {
      // Extract latitude
      String latStr = sentence.substring(commaIndex[1] + 1, commaIndex[2]);
      String latDir = sentence.substring(commaIndex[2] + 1, commaIndex[3]);
      
      if (latStr.length() > 0 && latDir.length() > 0) {
        double lat = latStr.substring(0, 2).toDouble() + 
                    latStr.substring(2).toDouble() / 60.0;
        if (latDir == "S") lat = -lat;
        gpsData->latitude = lat;
      }
      
      // Extract longitude
      String lonStr = sentence.substring(commaIndex[3] + 1, commaIndex[4]);
      String lonDir = sentence.substring(commaIndex[4] + 1, commaIndex[5]);
      
      if (lonStr.length() > 0 && lonDir.length() > 0) {
        double lon = lonStr.substring(0, 3).toDouble() + 
                    lonStr.substring(3).toDouble() / 60.0;
        if (lonDir == "W") lon = -lon;
        gpsData->longitude = lon;
      }
      
      // Extract fix quality
      String fixStr = sentence.substring(commaIndex[5] + 1, commaIndex[6]);
      gpsData->fix = (fixStr.toInt() > 0);
      
      // Extract number of satellites
      String satStr = sentence.substring(commaIndex[6] + 1, commaIndex[7]);
      gpsData->satellites = satStr.toInt();
      
      // Extract HDOP
      String hdopStr = sentence.substring(commaIndex[7] + 1, commaIndex[8]);
      if (hdopStr.length() > 0) {
        gpsData->hdop = hdopStr.toFloat();
      }
      
      // Extract altitude
      String altStr = sentence.substring(commaIndex[8] + 1, commaIndex[9]);
      if (altStr.length() > 0) {
        gpsData->altitude = altStr.toFloat();
      }
      
      return true;
    }
  } else if (sentence.startsWith("$GPRMC")) {
    // Parse RMC sentence (Recommended Minimum Course)
    int commaIndex[12];
    int commaCount = 0;
    
    // Find all comma positions
    for (int i = 0; i < sentence.length() && commaCount < 12; i++) {
      if (sentence.charAt(i) == ',') {
        commaIndex[commaCount] = i;
        commaCount++;
      }
    }
    
    if (commaCount >= 11) {
      // Extract speed (knots)
      String speedStr = sentence.substring(commaIndex[6] + 1, commaIndex[7]);
      if (speedStr.length() > 0) {
        gpsData->speed = speedStr.toFloat() * 0.514444f; // Convert knots to m/s
      }
      
      // Extract course
      String courseStr = sentence.substring(commaIndex[7] + 1, commaIndex[8]);
      if (courseStr.length() > 0) {
        gpsData->course = courseStr.toFloat();
      }
      
      return true;
    }
  }
  
  return false;
}

// Calculate distance between two GPS coordinates (Haversine formula)
float calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  const float R = 6371000.0f; // Earth radius in meters
  
  float dLat = (lat2 - lat1) * DEG_TO_RAD;
  float dLon = (lon2 - lon1) * DEG_TO_RAD;
  
  float a = sin(dLat / 2) * sin(dLat / 2) +
            cos(lat1 * DEG_TO_RAD) * cos(lat2 * DEG_TO_RAD) *
            sin(dLon / 2) * sin(dLon / 2);
  
  float c = 2 * atan2(sqrt(a), sqrt(1 - a));
  
  return R * c;
}

// Calculate bearing between two GPS coordinates
float calculateBearing(double lat1, double lon1, double lat2, double lon2) {
  float dLon = (lon2 - lon1) * DEG_TO_RAD;
  float lat1Rad = lat1 * DEG_TO_RAD;
  float lat2Rad = lat2 * DEG_TO_RAD;
  
  float y = sin(dLon) * cos(lat2Rad);
  float x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(dLon);
  
  float bearing = atan2(y, x) * RAD_TO_DEG;
  
  // Normalize to 0-360 degrees
  if (bearing < 0) {
    bearing += 360.0f;
  }
  
  return bearing;
}