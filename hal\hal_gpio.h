/*
 * QuadFly Flight Controller - GP<PERSON> HAL Header
 * 
 * Hardware abstraction for GPIO operations
 */

#ifndef HAL_GPIO_H
#define HAL_GPIO_H

#include "hal.h"

// GPIO interrupt types
typedef enum {
  HAL_GPIO_INTERRUPT_NONE = 0,
  HAL_GPIO_INTERRUPT_RISING = 1,
  H<PERSON>_GPIO_INTERRUPT_FALLING = 2,
  HAL_GPIO_INTERRUPT_CHANGE = 3,
  HAL_GPIO_INTERRUPT_LOW = 4,
  HAL_GPIO_INTERRUPT_HIGH = 5
} HAL_GPIO_InterruptType;

// GPIO callback function type
typedef void (*HAL_GPIO_Callback)(uint8_t pin);

// GPIO configuration structure
typedef struct {
  uint8_t pin;
  HAL_GPIO_Mode mode;
  HAL_GPIO_InterruptType interrupt_type;
  HAL_GPIO_Callback callback;
} HAL_GPIO_Config;

// Function declarations

// Basic GPIO operations
HAL_StatusTypeDef HAL_GPIO_Init(uint8_t pin, HAL_GPIO_Mode mode);
HAL_StatusTypeDef HAL_GPIO_DeInit(uint8_t pin);
void HAL_GPIO_WritePin(uint8_t pin, HAL_GPIO_PinState state);
HAL_GPIO_PinState HAL_GPIO_ReadPin(uint8_t pin);
void HAL_GPIO_TogglePin(uint8_t pin);

// Advanced GPIO operations
HAL_StatusTypeDef HAL_GPIO_ConfigPin(const HAL_GPIO_Config* config);
HAL_StatusTypeDef HAL_GPIO_SetMode(uint8_t pin, HAL_GPIO_Mode mode);
HAL_GPIO_Mode HAL_GPIO_GetMode(uint8_t pin);

// Interrupt operations
HAL_StatusTypeDef HAL_GPIO_EnableInterrupt(uint8_t pin, HAL_GPIO_InterruptType type, HAL_GPIO_Callback callback);
HAL_StatusTypeDef HAL_GPIO_DisableInterrupt(uint8_t pin);
void HAL_GPIO_ClearInterrupt(uint8_t pin);
bool HAL_GPIO_GetInterruptStatus(uint8_t pin);

// Bulk operations
HAL_StatusTypeDef HAL_GPIO_WritePort(uint32_t port_value, uint32_t pin_mask);
uint32_t HAL_GPIO_ReadPort(uint32_t pin_mask);

// Pin validation
bool HAL_GPIO_IsValidPin(uint8_t pin);
bool HAL_GPIO_IsPinAvailable(uint8_t pin);

// Pin capabilities
bool HAL_GPIO_SupportsInput(uint8_t pin);
bool HAL_GPIO_SupportsOutput(uint8_t pin);
bool HAL_GPIO_SupportsPullup(uint8_t pin);
bool HAL_GPIO_SupportsPulldown(uint8_t pin);
bool HAL_GPIO_SupportsInterrupt(uint8_t pin);

#endif // HAL_GPIO_H
