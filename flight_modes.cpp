/*
 * QuadFly Flight Controller - Flight Modes Implementation
 * 
 * Complete flight mode system with professional-grade control algorithms
 */

#include "flight_modes.h"

// Global variables for flight modes
static float targetRoll = 0.0f;
static float targetPitch = 0.0f;
static float targetYaw = 0.0f;
static float targetAltitude = 0.0f;
static double homeLatitude = 0.0;
static double homeLongitude = 0.0;
static float homeAltitude = 0.0f;
static bool homeSet = false;

// Initialize flight mode system
void initializeFlightModes(void) {
  Serial.println("Initializing flight modes...");
  
  // Reset all targets
  targetRoll = 0.0f;
  targetPitch = 0.0f;
  targetYaw = 0.0f;
  targetAltitude = 0.0f;
  
  // Reset home position
  homeSet = false;
  
  Serial.println("Flight modes initialized successfully");
}

// Process flight mode transitions
void processFlightModeTransitions(SystemState_t *state, FlightData_t *flightData) {
  FlightMode_t newFlightMode = state->flightMode;
  
  // Emergency mode override
  if (state->emergencyMode) {
    newFlightMode = FLIGHT_MODE_EMERGENCY;
  } else {
    // Normal flight mode selection based on receiver inputs
    uint16_t modeChannel = flightData->receiver.channels[4]; // Channel 5 for mode
    
    if (!state->armed) {
      newFlightMode = FLIGHT_MODE_DISARMED;
    } else if (modeChannel < 1300) {
      newFlightMode = FLIGHT_MODE_MANUAL;
    } else if (modeChannel < 1700) {
      newFlightMode = FLIGHT_MODE_STABILIZE;
    } else {
      // Check for GPS availability for advanced modes
      if (flightData->gps.fix && flightData->gps.satellites >= 6) {
        uint16_t auxChannel = flightData->receiver.channels[5]; // Channel 6 for aux
        
        if (auxChannel < 1300) {
          newFlightMode = FLIGHT_MODE_ALT_HOLD;
        } else if (auxChannel < 1700) {
          newFlightMode = FLIGHT_MODE_GPS_HOLD;
        } else {
          newFlightMode = FLIGHT_MODE_RTH;
        }
      } else {
        newFlightMode = FLIGHT_MODE_ALT_HOLD; // Fallback to altitude hold
      }
    }
  }
  
  // Handle flight mode changes
  if (newFlightMode != state->flightMode) {
    Serial.print("Flight mode change: ");
    Serial.print(state->flightMode);
    Serial.print(" -> ");
    Serial.println(newFlightMode);
    
    state->previousFlightMode = state->flightMode;
    state->flightMode = newFlightMode;
    
    // Reset PID controllers on mode change
    resetPID(&rollPID);
    resetPID(&pitchPID);
    resetPID(&yawPID);
    resetPID(&altitudePID);
    resetPID(&velocityPID);
    resetPID(&positionPID);
    
    // Mode-specific initialization
    switch (newFlightMode) {
      case FLIGHT_MODE_ALT_HOLD:
        targetAltitude = flightData->altitude.altitude;
        break;
        
      case FLIGHT_MODE_GPS_HOLD:
        targetAltitude = flightData->altitude.altitude;
        // Set position hold targets
        break;
        
      case FLIGHT_MODE_RTH:
        if (!homeSet && flightData->gps.fix) {
          homeLatitude = flightData->gps.latitude;
          homeLongitude = flightData->gps.longitude;
          homeAltitude = flightData->altitude.altitude;
          homeSet = true;
          Serial.println("Home position set for RTH");
        }
        break;
        
      default:
        break;
    }
  }
  
  // Check arming/disarming sequences
  armingSequenceCheck(state, flightData);
  disarmingSequenceCheck(state, flightData);
}

// Execute current flight mode
void executeFlightMode(SystemState_t *state, FlightData_t *flightData) {
  switch (state->flightMode) {
    case FLIGHT_MODE_DISARMED:
      // Motors should be off
      break;
      
    case FLIGHT_MODE_MANUAL:
      executeManualMode(state, flightData);
      break;
      
    case FLIGHT_MODE_STABILIZE:
      executeStabilizeMode(state, flightData);
      break;
      
    case FLIGHT_MODE_ALT_HOLD:
      executeAltHoldMode(state, flightData);
      break;
      
    case FLIGHT_MODE_GPS_HOLD:
      executeGPSHoldMode(state, flightData);
      break;
      
    case FLIGHT_MODE_RTH:
      executeRTHMode(state, flightData);
      break;
      
    case FLIGHT_MODE_EMERGENCY:
      executeEmergencyMode(state, flightData);
      break;
  }
}

// Manual/Acro mode - direct control
void executeManualMode(SystemState_t *state, FlightData_t *flightData) {
  // Direct motor control based on receiver inputs
  float throttle = (flightData->receiver.channels[2] - 1000) / 1000.0f; // Throttle (0-1)
  float roll = (flightData->receiver.channels[0] - 1500) / 500.0f;      // Roll (-1 to 1)
  float pitch = (flightData->receiver.channels[1] - 1500) / 500.0f;     // Pitch (-1 to 1)
  float yaw = (flightData->receiver.channels[3] - 1500) / 500.0f;       // Yaw (-1 to 1)
  
  // Apply expo curves for smoother control
  roll = roll * roll * roll * 0.3f + roll * 0.7f;
  pitch = pitch * pitch * pitch * 0.3f + pitch * 0.7f;
  yaw = yaw * yaw * yaw * 0.3f + yaw * 0.7f;
  
  // Convert to motor outputs
  calculateMotorMix(throttle, roll * 400.0f, pitch * 400.0f, yaw * 400.0f, flightData->motors.motor);
}

// Stabilize mode - angle stabilization
void executeStabilizeMode(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t lastUpdate = 0;
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  if (dt > 0.0f && dt < 0.1f) {
    // Calculate target angles from receiver inputs
    float maxAngle = 30.0f * DEG_TO_RAD; // 30 degree max tilt
    targetRoll = (flightData->receiver.channels[0] - 1500) / 500.0f * maxAngle;
    targetPitch = (flightData->receiver.channels[1] - 1500) / 500.0f * maxAngle;
    
    // Yaw rate control
    float targetYawRate = (flightData->receiver.channels[3] - 1500) / 500.0f * 2.0f; // 2 rad/s max
    
    // PID control for roll and pitch
    float rollOutput = updatePID(&rollPID, targetRoll, flightData->attitude.roll, dt);
    float pitchOutput = updatePID(&pitchPID, targetPitch, flightData->attitude.pitch, dt);
    float yawOutput = updatePID(&yawPID, targetYawRate, flightData->attitude.yawRate, dt);
    
    // Throttle from receiver
    float throttle = (flightData->receiver.channels[2] - 1000) / 1000.0f;
    
    // Calculate motor outputs
    calculateMotorMix(throttle, rollOutput, pitchOutput, yawOutput, flightData->motors.motor);
  }
  
  lastUpdate = currentTime;
}

// Altitude hold mode
void executeAltHoldMode(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t lastUpdate = 0;
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  if (dt > 0.0f && dt < 0.1f) {
    // Altitude control
    float throttleInput = (flightData->receiver.channels[2] - 1500) / 500.0f;
    
    // Adjust target altitude based on throttle stick
    if (abs(throttleInput) > 0.1f) {
      targetAltitude += throttleInput * 2.0f * dt; // 2 m/s climb rate
    }
    
    // PID control for altitude
    float altitudeOutput = updatePID(&altitudePID, targetAltitude, flightData->altitude.altitude, dt);
    
    // Base throttle plus altitude correction
    float throttle = 0.5f + (altitudeOutput / 1000.0f); // Convert to throttle
    throttle = constrain(throttle, 0.0f, 1.0f);
    
    // Attitude control (same as stabilize mode)
    float maxAngle = 30.0f * DEG_TO_RAD;
    targetRoll = (flightData->receiver.channels[0] - 1500) / 500.0f * maxAngle;
    targetPitch = (flightData->receiver.channels[1] - 1500) / 500.0f * maxAngle;
    float targetYawRate = (flightData->receiver.channels[3] - 1500) / 500.0f * 2.0f;
    
    float rollOutput = updatePID(&rollPID, targetRoll, flightData->attitude.roll, dt);
    float pitchOutput = updatePID(&pitchPID, targetPitch, flightData->attitude.pitch, dt);
    float yawOutput = updatePID(&yawPID, targetYawRate, flightData->attitude.yawRate, dt);
    
    // Calculate motor outputs
    calculateMotorMix(throttle, rollOutput, pitchOutput, yawOutput, flightData->motors.motor);
  }
  
  lastUpdate = currentTime;
}

// GPS hold mode (loiter)
void executeGPSHoldMode(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t lastUpdate = 0;
  static double targetLatitude = 0.0;
  static double targetLongitude = 0.0;
  static bool positionSet = false;
  
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  if (dt > 0.0f && dt < 0.1f) {
    // Set position target on first execution
    if (!positionSet && flightData->gps.fix) {
      targetLatitude = flightData->gps.latitude;
      targetLongitude = flightData->gps.longitude;
      positionSet = true;
      Serial.println("GPS hold position set");
    }
    
    if (positionSet && flightData->gps.fix) {
      // Calculate position errors (simplified)
      double latError = targetLatitude - flightData->gps.latitude;
      double lonError = targetLongitude - flightData->gps.longitude;
      
      // Convert to meters (approximate)
      float northError = latError * 111111.0f; // 1 degree ≈ 111km
      float eastError = lonError * 111111.0f * cos(flightData->gps.latitude * DEG_TO_RAD);
      
      // Position PID control
      float northOutput = updatePID(&positionPID, 0.0f, northError, dt);
      float eastOutput = updatePID(&positionPID, 0.0f, eastError, dt);
      
      // Convert to roll/pitch commands
      targetPitch = constrain(northOutput * DEG_TO_RAD, -20.0f * DEG_TO_RAD, 20.0f * DEG_TO_RAD);
      targetRoll = constrain(eastOutput * DEG_TO_RAD, -20.0f * DEG_TO_RAD, 20.0f * DEG_TO_RAD);
    }
    
    // Altitude control (same as altitude hold)
    float throttleInput = (flightData->receiver.channels[2] - 1500) / 500.0f;
    if (abs(throttleInput) > 0.1f) {
      targetAltitude += throttleInput * 2.0f * dt;
    }
    
    float altitudeOutput = updatePID(&altitudePID, targetAltitude, flightData->altitude.altitude, dt);
    float throttle = 0.5f + (altitudeOutput / 1000.0f);
    throttle = constrain(throttle, 0.0f, 1.0f);
    
    // Yaw control
    float targetYawRate = (flightData->receiver.channels[3] - 1500) / 500.0f * 2.0f;
    
    // PID control for attitude
    float rollOutput = updatePID(&rollPID, targetRoll, flightData->attitude.roll, dt);
    float pitchOutput = updatePID(&pitchPID, targetPitch, flightData->attitude.pitch, dt);
    float yawOutput = updatePID(&yawPID, targetYawRate, flightData->attitude.yawRate, dt);
    
    // Calculate motor outputs
    calculateMotorMix(throttle, rollOutput, pitchOutput, yawOutput, flightData->motors.motor);
  }
  
  lastUpdate = currentTime;
}

// Return to home mode
void executeRTHMode(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t lastUpdate = 0;
  static uint8_t rthPhase = 0; // 0: climb, 1: navigate, 2: descend, 3: land
  
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  if (dt > 0.0f && dt < 0.1f && homeSet && flightData->gps.fix) {
    // Calculate distance to home
    double latError = homeLatitude - flightData->gps.latitude;
    double lonError = homeLongitude - flightData->gps.longitude;
    float distanceToHome = sqrt(latError * latError + lonError * lonError) * 111111.0f;
    
    switch (rthPhase) {
      case 0: // Climb to RTH altitude
        targetAltitude = homeAltitude + 20.0f; // 20m above home
        if (flightData->altitude.altitude >= targetAltitude - 2.0f) {
          rthPhase = 1;
          Serial.println("RTH: Climbing complete, navigating home");
        }
        break;
        
      case 1: // Navigate to home
        if (distanceToHome < 5.0f) { // Within 5 meters of home
          rthPhase = 2;
          Serial.println("RTH: Above home, beginning descent");
        } else {
          // Navigate towards home
          float northError = latError * 111111.0f;
          float eastError = lonError * 111111.0f * cos(flightData->gps.latitude * DEG_TO_RAD);
          
          float northOutput = updatePID(&positionPID, 0.0f, northError, dt);
          float eastOutput = updatePID(&positionPID, 0.0f, eastError, dt);
          
          targetPitch = constrain(northOutput * DEG_TO_RAD, -15.0f * DEG_TO_RAD, 15.0f * DEG_TO_RAD);
          targetRoll = constrain(eastOutput * DEG_TO_RAD, -15.0f * DEG_TO_RAD, 15.0f * DEG_TO_RAD);
        }
        break;
        
      case 2: // Descend
        targetAltitude = homeAltitude + 1.0f; // 1m above home
        if (flightData->altitude.altitude <= targetAltitude + 1.0f) {
          rthPhase = 3;
          Serial.println("RTH: Beginning landing");
        }
        break;
        
      case 3: // Land
        targetAltitude -= 0.5f * dt; // Descend at 0.5 m/s
        if (flightData->altitude.altitude <= 0.5f) {
          // Land and disarm
          state->armed = false;
          state->flightMode = FLIGHT_MODE_DISARMED;
          Serial.println("RTH: Landing complete, disarmed");
        }
        break;
    }
    
    // Altitude control
    float altitudeOutput = updatePID(&altitudePID, targetAltitude, flightData->altitude.altitude, dt);
    float throttle = 0.5f + (altitudeOutput / 1000.0f);
    throttle = constrain(throttle, 0.0f, 1.0f);
    
    // Attitude control
    float rollOutput = updatePID(&rollPID, targetRoll, flightData->attitude.roll, dt);
    float pitchOutput = updatePID(&pitchPID, targetPitch, flightData->attitude.pitch, dt);
    float yawOutput = updatePID(&yawPID, 0.0f, flightData->attitude.yawRate, dt); // Hold heading
    
    // Calculate motor outputs
    calculateMotorMix(throttle, rollOutput, pitchOutput, yawOutput, flightData->motors.motor);
  }
  
  lastUpdate = currentTime;
}

// Emergency mode - immediate safe landing
void executeEmergencyMode(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t emergencyStartTime = 0;
  static bool emergencyInit = false;
  
  if (!emergencyInit) {
    emergencyStartTime = millis();
    emergencyInit = true;
    targetAltitude = flightData->altitude.altitude;
    Serial.println("EMERGENCY MODE ACTIVATED - Initiating safe landing");
  }
  
  // Gradual descent
  uint32_t emergencyDuration = millis() - emergencyStartTime;
  targetAltitude -= 0.3f * (emergencyDuration / 1000.0f); // 0.3 m/s descent
  
  // Level the aircraft
  targetRoll = 0.0f;
  targetPitch = 0.0f;
  
  static uint32_t lastUpdate = 0;
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  if (dt > 0.0f && dt < 0.1f) {
    // Attitude stabilization
    float rollOutput = updatePID(&rollPID, targetRoll, flightData->attitude.roll, dt);
    float pitchOutput = updatePID(&pitchPID, targetPitch, flightData->attitude.pitch, dt);
    float yawOutput = updatePID(&yawPID, 0.0f, flightData->attitude.yawRate, dt);
    
    // Altitude control
    float altitudeOutput = updatePID(&altitudePID, targetAltitude, flightData->altitude.altitude, dt);
    float throttle = 0.4f + (altitudeOutput / 1000.0f); // Lower base throttle for descent
    throttle = constrain(throttle, 0.2f, 0.8f);
    
    // Calculate motor outputs
    calculateMotorMix(throttle, rollOutput, pitchOutput, yawOutput, flightData->motors.motor);
    
    // Auto-disarm when on ground
    if (flightData->altitude.altitude <= 0.5f && emergencyDuration > 5000) {
      state->armed = false;
      state->flightMode = FLIGHT_MODE_DISARMED;
      state->emergencyMode = false;
      emergencyInit = false;
      Serial.println("Emergency landing complete - Disarmed");
    }
  }
  
  lastUpdate = currentTime;
}

// Calculate motor mix for quadcopter X configuration
void calculateMotorMix(float throttle, float roll, float pitch, float yaw, uint16_t *motors) {
  // X configuration motor mix
  float motor_fl = throttle - roll + pitch - yaw; // Front Left
  float motor_fr = throttle + roll + pitch + yaw; // Front Right
  float motor_bl = throttle - roll - pitch + yaw; // Back Left
  float motor_br = throttle + roll - pitch - yaw; // Back Right
  
  // Convert to PWM values (1000-2000µs)
  motors[0] = constrain(1000 + (motor_fl * 1000), 1000, 2000); // Front Left
  motors[1] = constrain(1000 + (motor_fr * 1000), 1000, 2000); // Front Right
  motors[2] = constrain(1000 + (motor_bl * 1000), 1000, 2000); // Back Left
  motors[3] = constrain(1000 + (motor_br * 1000), 1000, 2000); // Back Right
}

// Check arming sequence
void armingSequenceCheck(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t armingStartTime = 0;
  static bool armingSequenceActive = false;
  
  if (!state->armed && !state->emergencyMode) {
    // Arming sequence: Throttle low, Yaw right for 3 seconds
    bool throttleLow = flightData->receiver.channels[2] < 1100;
    bool yawRight = flightData->receiver.channels[3] > 1800;
    bool receiverOK = flightData->receiver.signalQuality > 50;
    
    if (throttleLow && yawRight && receiverOK) {
      if (!armingSequenceActive) {
        armingSequenceActive = true;
        armingStartTime = millis();
        Serial.println("Arming sequence started...");
      } else {
        if (millis() - armingStartTime > 3000) { // 3 second hold
          // Additional safety checks
          bool levelAttitude = (abs(flightData->attitude.roll) < 10.0f * DEG_TO_RAD) &&
                              (abs(flightData->attitude.pitch) < 10.0f * DEG_TO_RAD);
          bool sensorsOK = (state->systemHealth & HEALTH_IMU_OK) &&
                          (state->systemHealth & HEALTH_BARO_OK);
          
          if (levelAttitude && sensorsOK) {
            state->armed = true;
            state->flightMode = FLIGHT_MODE_STABILIZE;
            state->flightTime = 0;
            Serial.println("ARMED - Flight ready");
            
            // Beep sequence to confirm arming
            for (int i = 0; i < 3; i++) {
              digitalWrite(BUZZER_PIN, HIGH);
              delay(100);
              digitalWrite(BUZZER_PIN, LOW);
              delay(100);
            }
          } else {
            Serial.println("Arming failed - Safety checks not passed");
          }
          
          armingSequenceActive = false;
        }
      }
    } else {
      armingSequenceActive = false;
    }
  }
}

// Check disarming sequence
void disarmingSequenceCheck(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t disarmingStartTime = 0;
  static bool disarmingSequenceActive = false;
  
  if (state->armed && !state->emergencyMode) {
    // Disarming sequence: Throttle low, Yaw left for 2 seconds
    bool throttleLow = flightData->receiver.channels[2] < 1100;
    bool yawLeft = flightData->receiver.channels[3] < 1200;
    
    if (throttleLow && yawLeft) {
      if (!disarmingSequenceActive) {
        disarmingSequenceActive = true;
        disarmingStartTime = millis();
      } else {
        if (millis() - disarmingStartTime > 2000) { // 2 second hold
          state->armed = false;
          state->flightMode = FLIGHT_MODE_DISARMED;
          Serial.println("DISARMED");
          
          // Single beep to confirm disarming
          digitalWrite(BUZZER_PIN, HIGH);
          delay(200);
          digitalWrite(BUZZER_PIN, LOW);
          
          disarmingSequenceActive = false;
        }
      }
    } else {
      disarmingSequenceActive = false;
    }
  }
}