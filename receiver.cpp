/*
 * QuadFly Flight Controller - Receiver Handler Implementation
 * 
 * Individual PWM channel decoding for FlySky FS-TH9X
 */

#include "receiver.h"

// PWM decoding variables for individual channels
volatile uint16_t pwmChannels[7] = {1500, 1500, 1000, 1500, 1000, 1000, 1000};
volatile uint32_t pwmStartTime[7] = {0};
volatile bool pwmChannelUpdated[7] = {false};

// Channel pins array
const uint8_t channelPins[7] = {
  RC_THROTTLE_PIN,
  RC_ROLL_PIN,
  RC_PITCH_PIN,
  RC_YAW_PIN,
  RC_MODE_PIN,
  RC_AUX1_PIN,
  RC_AUX2_PIN
};

// Initialize receiver for individual PWM channels
bool initializeReceiver(void) {
  Serial.println("Initializing receiver for FS TH9X individual PWM channels...");
  
  // Configure all PWM input pins
  for (int i = 0; i < 7; i++) {
    pinMode(channelPins[i], INPUT_PULLUP);
    
    // Attach interrupts for each channel
    switch (i) {
      case 0: // Throttle
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), throttleInterrupt, CHANGE);
        break;
      case 1: // Roll
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), rollInterrupt, CHANGE);
        break;
      case 2: // Pitch
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), pitchInterrupt, CHANGE);
        break;
      case 3: // Yaw
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), yawInterrupt, CHANGE);
        break;
      case 4: // Mode
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), modeInterrupt, CHANGE);
        break;
      case 5: // AUX1
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), aux1Interrupt, CHANGE);
        break;
      case 6: // AUX2
        attachInterrupt(digitalPinToInterrupt(channelPins[i]), aux2Interrupt, CHANGE);
        break;
    }
  }
  
  Serial.println("Receiver initialized successfully for individual PWM channels");
  return true;
}

// Read receiver inputs
void readReceiverInputs(ReceiverData_t *receiverData) {
  static uint32_t lastValidFrame = 0;
  
  // Copy PWM channels atomically
  noInterrupts();
  for (int i = 0; i < 7; i++) {
    receiverData->channels[i] = pwmChannels[i];
  }
  interrupts();
  
  // Update signal quality and connection status
  uint32_t currentTime = millis();
  
  // Check if we have recent updates from any channel
  bool hasRecentUpdate = false;
  for (int i = 0; i < 7; i++) {
    if (pwmChannelUpdated[i]) {
      hasRecentUpdate = true;
      pwmChannelUpdated[i] = false;
    }
  }
  
  if (hasRecentUpdate) {
    lastValidFrame = currentTime;
    receiverData->lastUpdate = currentTime;
    receiverData->signalLost = false;
    receiverData->signalQuality = 100;
  } else {
    // Check for signal loss
    if (currentTime - lastValidFrame > RC_SIGNAL_TIMEOUT_MS) {
      receiverData->signalLost = true;
      receiverData->signalQuality = 0;
      
      // Set failsafe values
      receiverData->channels[RC_CHANNEL_ROLL] = 1500;     // Roll center
      receiverData->channels[RC_CHANNEL_PITCH] = 1500;    // Pitch center
      receiverData->channels[RC_CHANNEL_THROTTLE] = 1000; // Throttle minimum
      receiverData->channels[RC_CHANNEL_YAW] = 1500;      // Yaw center
      receiverData->channels[RC_CHANNEL_MODE] = 1000;     // Mode switch to manual
      receiverData->channels[RC_CHANNEL_AUX1] = 1000;     // Aux switches
      receiverData->channels[RC_CHANNEL_AUX2] = 1000;
    }
  }
}

// Interrupt handlers for each PWM channel
void IRAM_ATTR throttleInterrupt(void) {
  handlePWMInterrupt(0);
}

void IRAM_ATTR rollInterrupt(void) {
  handlePWMInterrupt(1);
}

void IRAM_ATTR pitchInterrupt(void) {
  handlePWMInterrupt(2);
}

void IRAM_ATTR yawInterrupt(void) {
  handlePWMInterrupt(3);
}

void IRAM_ATTR modeInterrupt(void) {
  handlePWMInterrupt(4);
}

void IRAM_ATTR aux1Interrupt(void) {
  handlePWMInterrupt(5);
}

void IRAM_ATTR aux2Interrupt(void) {
  handlePWMInterrupt(6);
}

// Generic PWM interrupt handler
void IRAM_ATTR handlePWMInterrupt(uint8_t channel) {
  uint32_t currentTime = micros();
  
  if (digitalRead(channelPins[channel]) == HIGH) {
    // Rising edge - start of pulse
    pwmStartTime[channel] = currentTime;
  } else {
    // Falling edge - end of pulse
    if (pwmStartTime[channel] != 0) {
      uint32_t pulseWidth = currentTime - pwmStartTime[channel];
      
      // Validate pulse width (typical RC range is 1000-2000µs)
      if (pulseWidth >= 800 && pulseWidth <= 2200) {
        pwmChannels[channel] = pulseWidth;
        pwmChannelUpdated[channel] = true;
      }
      
      pwmStartTime[channel] = 0;
    }
  }
}

// Get specific PWM channel value
uint16_t getPWMChannel(uint8_t channel) {
  if (channel < 7) {
    return pwmChannels[channel];
  }
  return 1500; // Default center value
}

/void checkSignalQuality(ReceiverData_t *receiverData) {
  static uint32_t lastQualityCheck = 0;
  static uint16_t frameCount = 0;
  static uint16_t goodFrames = 0;
  
  uint32_t currentTime = millis();
  
  // 🔹 Set connected flag based on lastUpdateTime
  if (currentTime - receiverData->lastUpdateTime > 100) {
    receiverData->connected = false;
  } else {
    receiverData->connected = true;
  }

  // 🔹 Evaluate signal quality once per second
  if (currentTime - lastQualityCheck >= 1000) {
    if (frameCount > 0) {
      receiverData->signalQuality = (goodFrames * 100) / frameCount;
    } else {
      receiverData->signalQuality = 0;
    }

    // Reset counters
    frameCount = 0;
    goodFrames = 0;
    lastQualityCheck = currentTime;
  }

  // 🔹 Count valid frames
  if (!receiverData->signalLost) {
    frameCount++;

    bool validFrame = true;
    for (int i = 0; i < 4; i++) {
      if (receiverData->channels[i] < 900 || receiverData->channels[i] > 2100) {
        validFrame = false;
        break;
      }
    }

    if (validFrame) {
      goodFrames++;
    }
  }
}


// Calibrate receiver (store min/max values)
void calibrateReceiver(void) {
  Serial.println("Starting receiver calibration...");
  Serial.println("Move all sticks and switches to their extremes");
  Serial.println("Calibration will complete automatically in 30 seconds");
  
  uint16_t minValues[7] = {2000, 2000, 2000, 2000, 2000, 2000, 2000};
  uint16_t maxValues[7] = {1000, 1000, 1000, 1000, 1000, 1000, 1000};
  
  uint32_t startTime = millis();
  
  while (millis() - startTime < 30000) { // 30 second calibration
    // Read current channel values
    for (int i = 0; i < 7; i++) {
      uint16_t channelValue = pwmChannels[i];
      
      if (channelValue < minValues[i]) {
        minValues[i] = channelValue;
      }
      if (channelValue > maxValues[i]) {
        maxValues[i] = channelValue;
      }
    }
    
    // Print progress every 5 seconds
    if ((millis() - startTime) % 5000 == 0) {
      Serial.print("Calibration in progress... ");
      Serial.print(30 - ((millis() - startTime) / 1000));
      Serial.println(" seconds remaining");
    }
    
    delay(100);
  }
  
  // Save calibration data
  Serial.println("Receiver calibration complete!");
  Serial.println("Calibration values:");
  
  for (int i = 0; i < 7; i++) {
    Serial.print("Channel ");
    Serial.print(i + 1);
    Serial.print(": Min=");
    Serial.print(minValues[i]);
    Serial.print(", Max=");
    Serial.println(maxValues[i]);
  }
}