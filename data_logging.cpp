/*
 * QuadFly Flight Controller - Data Logging Implementation
 * 
 * Professional flight data logging with circular buffer and SPIFFS storage
 */

#include "data_logging.h"

// Logging state
static File currentLogFile;
static bool loggingActive = false;
static uint32_t logFileNumber = 0;
static String currentLogFileName = "";

// Initialize data logging system
void initializeDataLogging(void) {
  Serial.println("Initializing data logging system...");
  
  // Initialize SPIFFS
  if (!SPIFFS.begin(true)) {
    Serial.println("ERROR: SPIFFS initialization failed");
    return;
  }
  
  // Check available space
  size_t totalBytes = SPIFFS.totalBytes();
  size_t usedBytes = SPIFFS.usedBytes();
  
  Serial.print("SPIFFS: ");
  Serial.print(usedBytes / 1024);
  Serial.print("KB used of ");
  Serial.print(totalBytes / 1024);
  Serial.println("KB total");
  
  // Find next log file number
  File root = SPIFFS.open("/");
  File file = root.openNextFile();
  
  while (file) {
    String fileName = file.name();
    if (fileName.startsWith("/log") && fileName.endsWith(".csv")) {
      // Extract file number
      int start = fileName.indexOf("log") + 3;
      int end = fileName.indexOf(".csv");
      uint32_t fileNum = fileName.substring(start, end).toInt();
      if (fileNum >= logFileNumber) {
        logFileNumber = fileNum + 1;
      }
    }
    file = root.openNextFile();
  }
  
  Serial.println("Data logging system initialized");
}

// Log flight data
void logFlightData(FlightData_t *flightData, SystemState_t *systemState) {
  static uint32_t lastLogTime = 0;
  uint32_t currentTime = millis();
  
  // Log at 10Hz when armed, 1Hz when disarmed
  uint32_t logInterval = systemState->armed ? 100 : 1000;
  
  if (currentTime - lastLogTime >= logInterval) {
    // Create new log file if needed
    if (!loggingActive) {
      createNewLogFile();
    }
    
    if (loggingActive && currentLogFile) {
      // Create log entry
      LogEntry_t logEntry;
      logEntry.timestamp = currentTime;
      logEntry.roll = flightData->attitude.roll * RAD_TO_DEG;
      logEntry.pitch = flightData->attitude.pitch * RAD_TO_DEG;
      logEntry.yaw = flightData->attitude.yaw * RAD_TO_DEG;
      logEntry.altitude = flightData->altitude.altitude;
      logEntry.batteryVoltage = flightData->battery.voltage;
      logEntry.flightMode = systemState->flightMode;
      logEntry.armed = systemState->armed;
      logEntry.gpsStatus = flightData->gps.satellites;
      
      // Write CSV format
      currentLogFile.print(logEntry.timestamp);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.roll, 2);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.pitch, 2);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.yaw, 2);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.altitude, 2);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.batteryVoltage, 2);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.flightMode);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.armed ? 1 : 0);
      currentLogFile.print(",");
      currentLogFile.print(logEntry.gpsStatus);
      currentLogFile.println();
      
      currentLogFile.flush(); // Ensure data is written
    }
    
    lastLogTime = currentTime;
  }
  
  // Close log file when disarmed for more than 30 seconds
  static uint32_t disarmedTime = 0;
  if (!systemState->armed) {
    if (disarmedTime == 0) {
      disarmedTime = currentTime;
    } else if (currentTime - disarmedTime > 30000 && loggingActive) {
      closeLogFile();
    }
  } else {
    disarmedTime = 0;
  }
}

// Create new log file
void createNewLogFile(void) {
  if (loggingActive) {
    closeLogFile();
  }
  
  // Generate filename
  currentLogFileName = "/log" + String(logFileNumber) + ".csv";
  logFileNumber++;
  
  // Open file for writing
  currentLogFile = SPIFFS.open(currentLogFileName, FILE_WRITE);
  
  if (currentLogFile) {
    loggingActive = true;
    
    // Write CSV header
    currentLogFile.println("Timestamp,Roll,Pitch,Yaw,Altitude,Battery,Mode,Armed,GPS");
    
    Serial.print("Created new log file: ");
    Serial.println(currentLogFileName);
  } else {
    Serial.println("ERROR: Failed to create log file");
  }
}

// Close current log file
void closeLogFile(void) {
  if (loggingActive && currentLogFile) {
    currentLogFile.close();
    loggingActive = false;
    
    Serial.print("Closed log file: ");
    Serial.println(currentLogFileName);
  }
}

// Download logs via serial
void downloadLogs(void) {
  Serial.println("=== Flight Logs Download ===");
  
  File root = SPIFFS.open("/");
  File file = root.openNextFile();
  
  while (file) {
    String fileName = file.name();
    if (fileName.startsWith("/log") && fileName.endsWith(".csv")) {
      Serial.print("File: ");
      Serial.print(fileName);
      Serial.print(" (");
      Serial.print(file.size());
      Serial.println(" bytes)");
      
      Serial.println("--- FILE START ---");
      
      while (file.available()) {
        Serial.write(file.read());
      }
      
      Serial.println("--- FILE END ---");
      Serial.println();
    }
    
    file.close();
    file = root.openNextFile();
  }
  
  Serial.println("=== Download Complete ===");
}

// Clear all log files
void clearLogs(void) {
  Serial.println("Clearing all log files...");
  
  File root = SPIFFS.open("/");
  File file = root.openNextFile();
  
  int deletedCount = 0;
  
  while (file) {
    String fileName = file.name();
    if (fileName.startsWith("/log") && fileName.endsWith(".csv")) {
      file.close();
      if (SPIFFS.remove(fileName)) {
        Serial.print("Deleted: ");
        Serial.println(fileName);
        deletedCount++;
      }
    } else {
      file.close();
    }
    
    file = root.openNextFile();
  }
  
  Serial.print("Deleted ");
  Serial.print(deletedCount);
  Serial.println(" log files");
  
  // Reset log file counter
  logFileNumber = 0;
}