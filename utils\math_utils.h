/*
 * QuadFly Flight Controller - Math Utilities Header
 * 
 * Common mathematical functions and utilities
 */

#ifndef MATH_UTILS_H
#define MATH_UTILS_H

#include <Arduino.h>
#include <math.h>

// Mathematical constants
#define PI_F 3.14159265358979323846f
#define DEG_TO_RAD_F (PI_F / 180.0f)
#define RAD_TO_DEG_F (180.0f / PI_F)

// Vector3 structure for 3D operations
typedef struct {
  float x, y, z;
} Vector3_t;

// Quaternion structure
typedef struct {
  float w, x, y, z;
} Quaternion_t;

// Matrix3x3 structure
typedef struct {
  float m[3][3];
} Matrix3x3_t;

// Basic math functions
float constrainFloat(float value, float min, float max);
float mapFloat(float value, float in_min, float in_max, float out_min, float out_max);
float deadbandFloat(float value, float deadband);
float lowPassFilter(float current, float previous, float alpha);
float highPassFilter(float current, float previous, float alpha);
float complementaryFilter(float angle, float gyro_rate, float dt, float alpha);

// Vector operations
Vector3_t vectorAdd(Vector3_t a, Vector3_t b);
Vector3_t vectorSubtract(Vector3_t a, Vector3_t b);
Vector3_t vectorScale(Vector3_t v, float scale);
float vectorMagnitude(Vector3_t v);
Vector3_t vectorNormalize(Vector3_t v);
float vectorDotProduct(Vector3_t a, Vector3_t b);
Vector3_t vectorCrossProduct(Vector3_t a, Vector3_t b);

// Quaternion operations
Quaternion_t quaternionMultiply(Quaternion_t a, Quaternion_t b);
Quaternion_t quaternionNormalize(Quaternion_t q);
Quaternion_t quaternionConjugate(Quaternion_t q);
void quaternionToEulerAngles(Quaternion_t q, float *roll, float *pitch, float *yaw);
Quaternion_t eulerToQuaternion(float roll, float pitch, float yaw);

// Matrix operations
Matrix3x3_t matrixMultiply(Matrix3x3_t a, Matrix3x3_t b);
Vector3_t matrixVectorMultiply(Matrix3x3_t m, Vector3_t v);
Matrix3x3_t matrixTranspose(Matrix3x3_t m);
Matrix3x3_t matrixInverse(Matrix3x3_t m);

// Angle utilities
float normalizeAngle(float angle);
float angleDifference(float angle1, float angle2);
float wrapAngle180(float angle);
float wrapAngle360(float angle);

// Statistical functions
float calculateMean(float *data, int count);
float calculateVariance(float *data, int count);
float calculateStandardDeviation(float *data, int count);

// Interpolation functions
float linearInterpolation(float x0, float y0, float x1, float y1, float x);
float cubicInterpolation(float p0, float p1, float p2, float p3, float t);

// Coordinate transformations
void bodyToEarthFrame(Vector3_t body, float roll, float pitch, float yaw, Vector3_t *earth);
void earthToBodyFrame(Vector3_t earth, float roll, float pitch, float yaw, Vector3_t *body);

// Fast math approximations
float fastSin(float x);
float fastCos(float x);
float fastAtan2(float y, float x);
float fastSqrt(float x);
float fastInvSqrt(float x);

#endif // MATH_UTILS_H
