/*
 * QuadFly Flight Controller - Memory Manager Header
 * 
 * Dynamic memory management with monitoring, statistics, and leak detection
 */

#ifndef MEMORY_MANAGER_H
#define MEMORY_MANAGER_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>

// Memory allocation types
typedef enum {
  MEM_TYPE_GENERAL = 0,
  MEM_TYPE_SENSOR_DATA = 1,
  MEM_TYPE_CONTROL_DATA = 2,
  MEM_TYPE_TELEMETRY = 3,
  MEM_TYPE_LOGGING = 4,
  MEM_TYPE_CONFIG = 5,
  MEM_TYPE_TEMPORARY = 6,
  MEM_TYPE_DMA = 7,
  MEM_TYPE_COUNT = 8
} MemoryType_t;

// Memory pool configuration
typedef struct {
  size_t block_size;
  uint16_t block_count;
  uint8_t alignment;
  bool enable_overflow_detection;
  bool enable_underflow_detection;
} MemoryPoolConfig_t;

// Memory allocation metadata
typedef struct {
  void* ptr;
  size_t size;
  MemoryType_t type;
  uint32_t timestamp;
  const char* file;
  int line;
  const char* function;
  uint32_t magic_header;
  uint32_t magic_footer;
  struct MemoryAllocation* next;
  struct MemoryAllocation* prev;
} MemoryAllocation_t;

// Memory statistics
typedef struct {
  uint32_t total_allocations;
  uint32_t total_deallocations;
  uint32_t current_allocations;
  uint32_t peak_allocations;
  
  size_t total_allocated_bytes;
  size_t total_deallocated_bytes;
  size_t current_allocated_bytes;
  size_t peak_allocated_bytes;
  
  uint32_t allocation_failures;
  uint32_t double_free_attempts;
  uint32_t invalid_free_attempts;
  uint32_t buffer_overflows;
  uint32_t buffer_underflows;
  
  size_t largest_allocation;
  size_t smallest_allocation;
  float average_allocation_size;
  
  uint32_t fragmentation_events;
  float fragmentation_ratio;
  size_t largest_free_block;
  
} MemoryStatistics_t;

// Memory pool structure
typedef struct {
  void* pool_start;
  size_t pool_size;
  size_t block_size;
  uint16_t total_blocks;
  uint16_t free_blocks;
  uint8_t* free_bitmap;
  SemaphoreHandle_t mutex;
  MemoryStatistics_t stats;
  bool initialized;
} MemoryPool_t;

// Memory leak detection
typedef struct {
  void* ptr;
  size_t size;
  uint32_t allocation_time;
  uint32_t last_access_time;
  const char* file;
  int line;
  const char* function;
  bool suspected_leak;
} MemoryLeak_t;

// Memory health status
typedef enum {
  MEMORY_HEALTH_EXCELLENT = 100,
  MEMORY_HEALTH_GOOD = 80,
  MEMORY_HEALTH_WARNING = 60,
  MEMORY_HEALTH_CRITICAL = 40,
  MEMORY_HEALTH_EMERGENCY = 20,
  MEMORY_HEALTH_FAILURE = 0
} MemoryHealth_t;

// Function declarations

// Initialization and configuration
bool MemoryManager_Init(void);
void MemoryManager_DeInit(void);
bool MemoryManager_CreatePool(MemoryType_t type, const MemoryPoolConfig_t* config);
bool MemoryManager_DestroyPool(MemoryType_t type);

// Memory allocation and deallocation
void* MemoryManager_Alloc(size_t size, MemoryType_t type);
void* MemoryManager_AllocZero(size_t size, MemoryType_t type);
void* MemoryManager_Realloc(void* ptr, size_t new_size);
void MemoryManager_Free(void* ptr);
bool MemoryManager_IsValidPointer(void* ptr);

// Debug allocation macros
#define MEM_ALLOC(size, type) MemoryManager_AllocDebug(size, type, __FILE__, __LINE__, __FUNCTION__)
#define MEM_ALLOC_ZERO(size, type) MemoryManager_AllocZeroDebug(size, type, __FILE__, __LINE__, __FUNCTION__)
#define MEM_FREE(ptr) MemoryManager_FreeDebug(ptr, __FILE__, __LINE__, __FUNCTION__)

void* MemoryManager_AllocDebug(size_t size, MemoryType_t type, const char* file, int line, const char* function);
void* MemoryManager_AllocZeroDebug(size_t size, MemoryType_t type, const char* file, int line, const char* function);
void MemoryManager_FreeDebug(void* ptr, const char* file, int line, const char* function);

// Pool-based allocation
void* MemoryManager_AllocFromPool(MemoryType_t type);
bool MemoryManager_FreeToPool(MemoryType_t type, void* ptr);
uint16_t MemoryManager_GetPoolFreeBlocks(MemoryType_t type);
float MemoryManager_GetPoolUtilization(MemoryType_t type);

// Memory statistics and monitoring
MemoryStatistics_t MemoryManager_GetStatistics(void);
MemoryStatistics_t MemoryManager_GetTypeStatistics(MemoryType_t type);
void MemoryManager_ResetStatistics(void);
void MemoryManager_UpdateStatistics(void);

// Memory health monitoring
MemoryHealth_t MemoryManager_GetHealth(void);
bool MemoryManager_CheckIntegrity(void);
uint32_t MemoryManager_GetCorruptionCount(void);
void MemoryManager_RunHealthCheck(void);

// Leak detection
void MemoryManager_EnableLeakDetection(bool enable);
uint32_t MemoryManager_ScanForLeaks(void);
uint32_t MemoryManager_GetLeakCount(void);
bool MemoryManager_GetLeakInfo(uint32_t index, MemoryLeak_t* leak_info);
void MemoryManager_ClearLeakHistory(void);

// Fragmentation analysis
float MemoryManager_GetFragmentationRatio(void);
size_t MemoryManager_GetLargestFreeBlock(void);
uint32_t MemoryManager_GetFreeBlockCount(void);
bool MemoryManager_DefragmentMemory(void);

// Buffer overflow/underflow detection
bool MemoryManager_EnableGuardBytes(bool enable);
bool MemoryManager_CheckGuardBytes(void* ptr);
uint32_t MemoryManager_GetOverflowCount(void);
uint32_t MemoryManager_GetUnderflowCount(void);

// Memory access tracking
void MemoryManager_TrackAccess(void* ptr);
uint32_t MemoryManager_GetAccessCount(void* ptr);
uint32_t MemoryManager_GetLastAccessTime(void* ptr);

// Emergency memory management
void MemoryManager_EnableEmergencyMode(bool enable);
bool MemoryManager_IsEmergencyMode(void);
void* MemoryManager_EmergencyAlloc(size_t size);
void MemoryManager_FreeEmergencyMemory(void);

// Memory pressure handling
typedef void (*MemoryPressureCallback_t)(MemoryType_t type, size_t requested_size);
bool MemoryManager_RegisterPressureCallback(MemoryPressureCallback_t callback);
void MemoryManager_SetPressureThreshold(float threshold_percent);
bool MemoryManager_IsMemoryPressure(void);

// Garbage collection
void MemoryManager_EnableGarbageCollection(bool enable);
void MemoryManager_RunGarbageCollection(void);
void MemoryManager_SetGCThreshold(float threshold);
uint32_t MemoryManager_GetGCRunCount(void);

// Memory alignment
void* MemoryManager_AllocAligned(size_t size, size_t alignment, MemoryType_t type);
bool MemoryManager_IsAligned(void* ptr, size_t alignment);
size_t MemoryManager_GetAlignment(void* ptr);

// Memory copying and manipulation
void* MemoryManager_SafeMemcpy(void* dest, const void* src, size_t n);
void* MemoryManager_SafeMemset(void* s, int c, size_t n);
int MemoryManager_SafeMemcmp(const void* s1, const void* s2, size_t n);

// Memory mapping and protection
bool MemoryManager_ProtectMemory(void* ptr, size_t size, bool read_only);
bool MemoryManager_UnprotectMemory(void* ptr, size_t size);
bool MemoryManager_IsMemoryProtected(void* ptr);

// Reporting and diagnostics
void MemoryManager_PrintReport(void);
void MemoryManager_PrintPoolReport(MemoryType_t type);
void MemoryManager_PrintLeakReport(void);
void MemoryManager_PrintFragmentationReport(void);
void MemoryManager_LogMemoryUsage(void);

// Configuration
typedef struct {
  bool enable_leak_detection;
  bool enable_guard_bytes;
  bool enable_access_tracking;
  bool enable_garbage_collection;
  bool enable_emergency_mode;
  uint32_t leak_scan_interval_ms;
  uint32_t health_check_interval_ms;
  float pressure_threshold_percent;
  float gc_threshold_percent;
  size_t emergency_pool_size;
} MemoryManagerConfig_t;

bool MemoryManager_Configure(const MemoryManagerConfig_t* config);
MemoryManagerConfig_t MemoryManager_GetConfiguration(void);

// Platform-specific functions
size_t MemoryManager_GetTotalHeapSize(void);
size_t MemoryManager_GetFreeHeapSize(void);
size_t MemoryManager_GetMinFreeHeapSize(void);
size_t MemoryManager_GetMaxAllocatableSize(void);

// Error handling
typedef enum {
  MEM_ERROR_NONE = 0,
  MEM_ERROR_OUT_OF_MEMORY,
  MEM_ERROR_INVALID_POINTER,
  MEM_ERROR_DOUBLE_FREE,
  MEM_ERROR_BUFFER_OVERFLOW,
  MEM_ERROR_BUFFER_UNDERFLOW,
  MEM_ERROR_CORRUPTION,
  MEM_ERROR_LEAK_DETECTED,
  MEM_ERROR_FRAGMENTATION,
  MEM_ERROR_POOL_FULL,
  MEM_ERROR_ALIGNMENT,
  MEM_ERROR_UNKNOWN
} MemoryError_t;

MemoryError_t MemoryManager_GetLastError(void);
const char* MemoryManager_GetErrorString(MemoryError_t error);
void MemoryManager_ClearError(void);

// Callback types
typedef void (*MemoryErrorCallback_t)(MemoryError_t error, void* ptr, size_t size);
typedef void (*MemoryLeakCallback_t)(const MemoryLeak_t* leak);

bool MemoryManager_RegisterErrorCallback(MemoryErrorCallback_t callback);
bool MemoryManager_RegisterLeakCallback(MemoryLeakCallback_t callback);

#endif // MEMORY_MANAGER_H
