/*
 * QuadFly Flight Controller - Safety Systems Header
 * 
 * Comprehensive safety monitoring and emergency handling
 */

#ifndef SAFETY_SYSTEMS_H
#define SAFETY_SYSTEMS_H

#include "config.h"

// Safety warning levels
typedef enum {
  SAFETY_LEVEL_OK = 0,
  SAFETY_LEVEL_WARNING = 1,
  SAFETY_LEVEL_CRITICAL = 2,
  SAFETY_LEVEL_EMERGENCY = 3
} SafetyLevel_t;

// Function declarations
void initializeSafetySystems(void);
void checkSafetySystems(SystemState_t *state, FlightData_t *flightData);
void checkFailsafeConditions(SystemState_t *state, FlightData_t *flightData);
void handleEmergencyMode(SystemState_t *state, FlightData_t *flightData);
void updateBatteryMonitoring(BatteryData_t *battery);

// Individual safety checks
SafetyLevel_t checkAttitudeSafety(AttitudeData_t *attitude);
SafetyLevel_t checkAltitudeSafety(AltitudeData_t *altitude);
SafetyLevel_t checkGPSSafety(GPSData_t *gps);
SafetyLevel_t checkBatterySafety(BatteryData_t *battery);
SafetyLevel_t checkReceiverSafety(ReceiverData_t *receiver);

#endif // SAFETY_SYSTEMS_H