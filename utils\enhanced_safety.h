/*
 * QuadFly Flight Controller - Enhanced Safety Systems Header
 * 
 * Multi-stage failsafes, geofencing, RTH, and redundant sensor voting
 */

#ifndef ENHANCED_SAFETY_H
#define ENHANCED_SAFETY_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include "math_utils.h"

// Failsafe levels
typedef enum {
  FAILSAFE_LEVEL_NONE = 0,
  FAILSAFE_LEVEL_WARNING = 1,
  FAILSAFE_LEVEL_CAUTION = 2,
  FAILSAFE_LEVEL_CRITICAL = 3,
  FAILSAFE_LEVEL_EMERGENCY = 4,
  FAILSAFE_LEVEL_CATASTROPHIC = 5
} FailsafeLevel_t;

// Failsafe triggers
typedef enum {
  FAILSAFE_TRIGGER_RC_LOSS = 0,
  FAILSAFE_TRIGGER_LOW_BATTERY = 1,
  FAILSAFE_TRIGGER_CRITICAL_BATTERY = 2,
  FAILSAFE_TRIGGER_GPS_LOSS = 3,
  FAILSAFE_TRIGGER_SENSOR_FAILURE = 4,
  FAILSAFE_TRIGGER_GEOFENCE_BREACH = 5,
  FAILSAFE_TRIGGER_ALTITUDE_LIMIT = 6,
  FAILSAFE_TRIGGER_SPEED_LIMIT = 7,
  FAILSAFE_TRIGGER_TILT_LIMIT = 8,
  FAILSAFE_TRIGGER_MOTOR_FAILURE = 9,
  FAILSAFE_TRIGGER_TEMPERATURE = 10,
  FAILSAFE_TRIGGER_VIBRATION = 11,
  FAILSAFE_TRIGGER_MANUAL = 12,
  FAILSAFE_TRIGGER_COUNT = 13
} FailsafeTrigger_t;

// Failsafe actions
typedef enum {
  FAILSAFE_ACTION_NONE = 0,
  FAILSAFE_ACTION_WARNING_BEEP = 1,
  FAILSAFE_ACTION_LED_FLASH = 2,
  FAILSAFE_ACTION_REDUCE_POWER = 3,
  FAILSAFE_ACTION_STABILIZE = 4,
  FAILSAFE_ACTION_ALTITUDE_HOLD = 5,
  FAILSAFE_ACTION_RETURN_TO_HOME = 6,
  FAILSAFE_ACTION_EMERGENCY_LAND = 7,
  FAILSAFE_ACTION_MOTOR_CUTOFF = 8,
  FAILSAFE_ACTION_DEPLOY_PARACHUTE = 9
} FailsafeAction_t;

// Geofence types
typedef enum {
  GEOFENCE_TYPE_CYLINDER = 0,
  GEOFENCE_TYPE_SPHERE = 1,
  GEOFENCE_TYPE_BOX = 2,
  GEOFENCE_TYPE_POLYGON = 3,
  GEOFENCE_TYPE_CORRIDOR = 4
} GeofenceType_t;

// Geofence structure
typedef struct {
  GeofenceType_t type;
  Vector3_t center;
  float radius;
  float height_min;
  float height_max;
  Vector3_t vertices[8];  // For polygon/box geofences
  uint8_t vertex_count;
  bool enabled;
  bool breach_detected;
  uint32_t breach_time;
  FailsafeAction_t breach_action;
} Geofence_t;

// Return-to-Home configuration
typedef struct {
  Vector3_t home_position;
  float return_altitude;
  float return_speed;
  float landing_speed;
  float hover_time_before_land;
  bool auto_land_enabled;
  bool precision_landing;
  uint32_t timeout_ms;
  bool enabled;
} RTHConfig_t;

// Battery failsafe configuration
typedef struct {
  uint16_t warning_voltage_mv;
  uint16_t critical_voltage_mv;
  uint16_t emergency_voltage_mv;
  uint8_t warning_capacity_percent;
  uint8_t critical_capacity_percent;
  uint8_t emergency_capacity_percent;
  uint32_t warning_time_remaining_s;
  uint32_t critical_time_remaining_s;
  bool enable_smart_rth;
  bool enable_voltage_compensation;
} BatteryFailsafeConfig_t;

// Sensor voting system
typedef struct {
  uint8_t sensor_count;
  float sensor_values[4];
  float sensor_weights[4];
  bool sensor_valid[4];
  uint32_t sensor_timestamps[4];
  float voted_value;
  float confidence;
  uint8_t failed_sensors;
  bool voting_active;
} SensorVoting_t;

// Failsafe state
typedef struct {
  FailsafeLevel_t current_level;
  FailsafeLevel_t previous_level;
  uint32_t active_triggers;
  uint32_t trigger_timestamps[FAILSAFE_TRIGGER_COUNT];
  uint32_t level_change_time;
  bool manual_override;
  bool system_armed;
} FailsafeState_t;

// Safety limits
typedef struct {
  float max_altitude_m;
  float max_distance_m;
  float max_speed_ms;
  float max_vertical_speed_ms;
  float max_tilt_angle_deg;
  float max_yaw_rate_dps;
  float min_battery_voltage_mv;
  float max_temperature_c;
  float max_vibration_g;
  uint32_t max_flight_time_s;
} SafetyLimits_t;

// Function declarations

// Initialization and configuration
bool EnhancedSafety_Init(void);
void EnhancedSafety_DeInit(void);
bool EnhancedSafety_Configure(const SafetyLimits_t* limits);
void EnhancedSafety_SetFailsafeLevel(FailsafeLevel_t level);
void EnhancedSafety_EnableManualOverride(bool enable);

// Failsafe management
void EnhancedSafety_UpdateFailsafeState(void);
bool EnhancedSafety_TriggerFailsafe(FailsafeTrigger_t trigger);
bool EnhancedSafety_ClearFailsafe(FailsafeTrigger_t trigger);
FailsafeLevel_t EnhancedSafety_GetFailsafeLevel(void);
bool EnhancedSafety_IsFailsafeActive(FailsafeTrigger_t trigger);
uint32_t EnhancedSafety_GetActiveFailsafes(void);

// Multi-stage failsafe actions
void EnhancedSafety_ExecuteFailsafeAction(FailsafeAction_t action);
void EnhancedSafety_ConfigureFailsafeResponse(FailsafeTrigger_t trigger, FailsafeLevel_t level, FailsafeAction_t action);
void EnhancedSafety_EscalateFailsafe(FailsafeTrigger_t trigger);
void EnhancedSafety_DeescalateFailsafe(FailsafeTrigger_t trigger);

// Geofencing
bool EnhancedSafety_CreateGeofence(uint8_t fence_id, const Geofence_t* fence);
bool EnhancedSafety_UpdateGeofence(uint8_t fence_id, const Geofence_t* fence);
bool EnhancedSafety_DeleteGeofence(uint8_t fence_id);
bool EnhancedSafety_EnableGeofence(uint8_t fence_id, bool enable);
bool EnhancedSafety_CheckGeofence(uint8_t fence_id, Vector3_t position);
bool EnhancedSafety_IsInsideGeofence(uint8_t fence_id, Vector3_t position);
Vector3_t EnhancedSafety_GetGeofenceViolationVector(uint8_t fence_id, Vector3_t position);

// Return-to-Home
bool EnhancedSafety_ConfigureRTH(const RTHConfig_t* config);
bool EnhancedSafety_SetHomePosition(Vector3_t position);
Vector3_t EnhancedSafety_GetHomePosition(void);
bool EnhancedSafety_StartRTH(void);
bool EnhancedSafety_CancelRTH(void);
bool EnhancedSafety_IsRTHActive(void);
float EnhancedSafety_GetRTHProgress(void);
uint32_t EnhancedSafety_GetRTHTimeRemaining(void);

// Battery failsafe
bool EnhancedSafety_ConfigureBatteryFailsafe(const BatteryFailsafeConfig_t* config);
void EnhancedSafety_UpdateBatteryStatus(float voltage, float current, uint8_t capacity_percent);
bool EnhancedSafety_CheckBatteryFailsafe(void);
uint32_t EnhancedSafety_EstimateFlightTimeRemaining(void);
bool EnhancedSafety_CanReachHome(void);
float EnhancedSafety_GetBatteryFailsafeLevel(void);

// Sensor voting system
bool EnhancedSafety_InitSensorVoting(uint8_t sensor_type, uint8_t sensor_count);
void EnhancedSafety_UpdateSensorVote(uint8_t sensor_type, uint8_t sensor_id, float value, bool valid);
float EnhancedSafety_GetVotedSensorValue(uint8_t sensor_type);
float EnhancedSafety_GetSensorConfidence(uint8_t sensor_type);
bool EnhancedSafety_IsSensorFailed(uint8_t sensor_type, uint8_t sensor_id);
void EnhancedSafety_DisableSensor(uint8_t sensor_type, uint8_t sensor_id);

// Safety monitoring
void EnhancedSafety_MonitorSafetyLimits(Vector3_t position, Vector3_t velocity, Vector3_t attitude);
bool EnhancedSafety_CheckAltitudeLimit(float altitude);
bool EnhancedSafety_CheckDistanceLimit(Vector3_t position);
bool EnhancedSafety_CheckSpeedLimit(Vector3_t velocity);
bool EnhancedSafety_CheckTiltLimit(Vector3_t attitude);
bool EnhancedSafety_CheckTemperatureLimit(float temperature);

// Emergency procedures
void EnhancedSafety_EmergencyLand(void);
void EnhancedSafety_EmergencyStop(void);
void EnhancedSafety_DeployParachute(void);
bool EnhancedSafety_IsEmergencyActive(void);
void EnhancedSafety_CancelEmergency(void);

// Motor failure detection and handling
bool EnhancedSafety_DetectMotorFailure(uint8_t motor_id);
void EnhancedSafety_HandleMotorFailure(uint8_t motor_id);
bool EnhancedSafety_CanContinueWithFailedMotor(uint8_t motor_id);
void EnhancedSafety_CompensateMotorFailure(uint8_t motor_id, float* motor_outputs);

// Wind and weather safety
void EnhancedSafety_UpdateWindConditions(float wind_speed, float wind_direction);
bool EnhancedSafety_CheckWindLimits(void);
bool EnhancedSafety_IsWeatherSafeForFlight(void);
void EnhancedSafety_AdaptToWindConditions(void);

// Collision avoidance
bool EnhancedSafety_InitCollisionAvoidance(void);
void EnhancedSafety_UpdateObstacleData(Vector3_t* obstacles, uint8_t count);
bool EnhancedSafety_CheckCollisionRisk(Vector3_t position, Vector3_t velocity);
Vector3_t EnhancedSafety_GetAvoidanceVector(Vector3_t position, Vector3_t velocity);

// Safety reporting and logging
void EnhancedSafety_LogSafetyEvent(FailsafeTrigger_t trigger, FailsafeLevel_t level, const char* description);
void EnhancedSafety_PrintSafetyStatus(void);
void EnhancedSafety_PrintFailsafeHistory(void);
uint32_t EnhancedSafety_GetSafetyEventCount(FailsafeTrigger_t trigger);

// Configuration and calibration
bool EnhancedSafety_SaveConfiguration(void);
bool EnhancedSafety_LoadConfiguration(void);
void EnhancedSafety_ResetToDefaults(void);
bool EnhancedSafety_CalibrateFailsafeThresholds(void);

// Advanced features
void EnhancedSafety_EnablePredictiveFailsafe(bool enable);
void EnhancedSafety_EnableAdaptiveThresholds(bool enable);
void EnhancedSafety_SetFailsafeTimeout(FailsafeTrigger_t trigger, uint32_t timeout_ms);
bool EnhancedSafety_TestFailsafeSystem(void);

// Callback types
typedef void (*SafetyEventCallback_t)(FailsafeTrigger_t trigger, FailsafeLevel_t level);
typedef void (*GeofenceBreachCallback_t)(uint8_t fence_id, Vector3_t position);
typedef void (*RTHCallback_t)(bool started, float progress);

bool EnhancedSafety_RegisterSafetyCallback(SafetyEventCallback_t callback);
bool EnhancedSafety_RegisterGeofenceCallback(GeofenceBreachCallback_t callback);
bool EnhancedSafety_RegisterRTHCallback(RTHCallback_t callback);

// Error handling
typedef enum {
  SAFETY_ERROR_NONE = 0,
  SAFETY_ERROR_INVALID_CONFIG,
  SAFETY_ERROR_SENSOR_FAILURE,
  SAFETY_ERROR_GEOFENCE_VIOLATION,
  SAFETY_ERROR_BATTERY_CRITICAL,
  SAFETY_ERROR_MOTOR_FAILURE,
  SAFETY_ERROR_COMMUNICATION_LOSS,
  SAFETY_ERROR_SYSTEM_OVERLOAD,
  SAFETY_ERROR_UNKNOWN
} SafetyError_t;

SafetyError_t EnhancedSafety_GetLastError(void);
const char* EnhancedSafety_GetErrorString(SafetyError_t error);

#endif // ENHANCED_SAFETY_H
