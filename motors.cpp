/*
 * QuadFly Flight Controller - Motor Control Implementation
 * 
 * Professional ESC control with safety features
 */
#include "motors.h"


// PWM channels for motors
const uint8_t motorChannels[4] = {0, 1, 2, 3};
const uint8_t motorPins[4] = {MOTOR_FL_PIN, MOTOR_FR_PIN, MOTOR_RR_PIN, MOTOR_RL_PIN};

// Initialize motor control
bool initializeMotors(void) {
  Serial.println("Initializing motors...");
  
  // Configure PWM for all motors
  for (int i = 0; i < 4; i++) {
    // Use newer ESP32 Arduino Core API
    ledcAttach(motorPins[i], 50, 16); // Pin, frequency, resolution
    
    // Set motors to minimum throttle
    setMotorSpeed(i, 1000);
  }
  
  Serial.println("Motors initialized successfully");
  Serial.println("WARNING: Ensure ESCs are calibrated before first flight!");
  
  return true;
}

// Update motor outputs
void updateMotorOutputs(MotorData_t *motorData) {
  if (motorData->motorsArmed) {
    for (int i = 0; i < 4; i++) {
      setMotorSpeed(i, motorData->motor[i]);
    }
  } else {
    setAllMotorsToMin();
  }
}

// Set all motors to minimum throttle
void setAllMotorsToMin(void) {
  for (int i = 0; i < 4; i++) {
    setMotorSpeed(i, 1000);
  }
}

// Set individual motor speed
void setMotorSpeed(uint8_t motor, uint16_t speed) {
  if (motor < 4) {
    // Constrain speed to valid PWM range
    speed = constrain(speed, 1000, 2000);
    
    // Convert to PWM duty cycle (16-bit resolution at 50Hz)
    uint32_t dutyCycle = map(speed, 1000, 2000, 3276, 6553); // 5% to 10% duty cycle
    
    ledcWrite(motorPins[motor], dutyCycle);
  }
}

// Calibrate ESCs
void calibrateESCs(void) {
  Serial.println("Starting ESC calibration...");
  Serial.println("WARNING: Remove propellers before calibration!");
  Serial.println("Press any key to continue...");
  
  while (!Serial.available()) {
    delay(100);
  }
  Serial.read(); // Clear input
  
  Serial.println("Setting maximum throttle...");
  
  // Set all motors to maximum
  for (int i = 0; i < 4; i++) {
    setMotorSpeed(i, 2000);
  }
  
  Serial.println("Connect battery and wait for ESC confirmation tones");
  Serial.println("Press any key when ready to set minimum throttle...");
  
  while (!Serial.available()) {
    delay(100);
  }
  Serial.read(); // Clear input
  
  Serial.println("Setting minimum throttle...");
  
  // Set all motors to minimum
  setAllMotorsToMin();
  
  Serial.println("ESC calibration complete!");
  Serial.println("ESCs should now be calibrated and ready for use");
  
  delay(2000);
}

// Arm motors (enable motor outputs)
void armMotors(void) {
  Serial.println("Motors ARMED");
  // Motors will be controlled by flight controller
}

// Disarm motors (disable motor outputs)
void disarmMotors(void) {
  Serial.println("Motors DISARMED");
  setAllMotorsToMin();
}