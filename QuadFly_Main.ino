/*
 * QuadFly Flight Controller - Main Application
 * 
 * Professional quadcopter flight controller firmware for ESP32
 */

#include "config.h"
#include "sensors.h"
#include "pid_controller.h"
#include "flight_modes.h"
#include "receiver.h"
#include "motors.h"
#include "gps_handler.h"
#include "safety_systems.h"
#include "calibration.h"
#include "data_logging.h"

// Global system data structures
SensorData_t systemSensors;
AttitudeData_t systemAttitude;
GPSData_t systemGPS;
ReceiverData_t systemReceiver;
MotorData_t systemMotors;
BatteryData_t systemBattery;
AltitudeData_t systemAltitude;
SystemState_t systemState;

// Task handles (removed telemetry task)
TaskHandle_t sensorTaskHandle = NULL;
TaskHandle_t controlTaskHandle = NULL;
TaskHandle_t safetyTaskHandle = NULL;

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("QuadFly Flight Controller v" QUADFLY_VERSION);
  Serial.println("Initializing systems...");
  
  // Initialize hardware
  initializeHardware();
  
  // Load configuration
  loadConfiguration();
  
  // Initialize all subsystems
  if (!initializeSensors()) {
    Serial.println("CRITICAL ERROR: Sensor initialization failed!");
    while(1) delay(1000);
  }
  
  if (!initializeMotors()) {
    Serial.println("CRITICAL ERROR: Motor initialization failed!");
    while(1) delay(1000);
  }
  
  if (!initializeReceiver()) {
    Serial.println("CRITICAL ERROR: Receiver initialization failed!");
    while(1) delay(1000);
  }
  
  initializeGPS();
  initializeSafetySystems();
  initializeDataLogging();
  
  // Initialize PID controllers
  initializePIDControllers();
  initializeFlightModes();
  
  Serial.println("QuadFly Flight Controller initialized successfully!");
  Serial.println("Ready for flight operations.");
  
  // Create tasks with increased stack sizes (no telemetry task)
  xTaskCreatePinnedToCore(
    sensorTask,           // Task function
    "SensorTask",         // Task name
    8192,                 // Stack size
    NULL,                 // Parameters
    3,                    // Priority
    &sensorTaskHandle,    // Task handle
    1                     // Core ID
  );
  
  xTaskCreatePinnedToCore(
    controlTask,          // Task function
    "ControlTask",        // Task name
    8192,                 // Stack size
    NULL,                 // Parameters
    2,                    // Priority
    &controlTaskHandle,   // Task handle
    1                     // Core ID
  );
  
  xTaskCreatePinnedToCore(
    safetyTask,           // Task function
    "SafetyTask",         // Task name
    6144,                 // Stack size
    NULL,                 // Parameters
    4,                    // Priority (highest)
    &safetyTaskHandle,    // Task handle
    0                     // Core ID
  );
}

void loop() {
  static uint32_t lastStatusUpdate = 0;
  
  // TEMPORARY ARMING TEST - Move this to flight_modes.cpp later
  static uint32_t lastArmCheck = 0;
  if (millis() - lastArmCheck > 100) { // Check every 100ms
    uint16_t throttle = systemReceiver.channels[RC_CHANNEL_THROTTLE];
    uint16_t yaw = systemReceiver.channels[RC_CHANNEL_YAW];
    uint16_t aux1 = systemReceiver.channels[RC_CHANNEL_AUX1];
    
    // ESC CALIBRATION MODE: AUX1 high + throttle high + yaw right
    static bool escCalibrationMode = false;
    static uint32_t calibrationStep = 0;
    static uint32_t calibrationStartTime = 0;
    
    if (!systemState.armed && aux1 > 1800 && throttle > 1800 && yaw > 1800) {
      if (!escCalibrationMode) {
        escCalibrationMode = true;
        calibrationStep = 1;
        calibrationStartTime = millis();
        Serial.println("=== ESC CALIBRATION MODE ACTIVATED ===");
        Serial.println("STEP 1: Disconnect battery power NOW!");
        Serial.println("You have 10 seconds...");
        
        // Set all motors to maximum for calibration
        for (int i = 0; i < 4; i++) {
          setMotorSpeed(i, 2000);
        }
      }
      
      // Handle calibration steps
      if (escCalibrationMode) {
        uint32_t elapsed = millis() - calibrationStartTime;
        
        switch (calibrationStep) {
          case 1: // Wait for battery disconnect (10 seconds)
            if (elapsed > 10000) {
              calibrationStep = 2;
              calibrationStartTime = millis();
              Serial.println("STEP 2: Connect battery power NOW!");
              Serial.println("You should hear ESC beeps...");
            }
            break;
            
          case 2: // Wait for high signal registration (5 seconds)
            if (elapsed > 5000) {
              calibrationStep = 3;
              calibrationStartTime = millis();
              Serial.println("STEP 3: Setting LOW throttle signal...");
              
              // Set all motors to minimum
              for (int i = 0; i < 4; i++) {
                setMotorSpeed(i, 1000);
              }
            }
            break;
            
          case 3: // Wait for low signal registration (3 seconds)
            if (elapsed > 3000) {
              calibrationStep = 4;
              Serial.println("=== ESC CALIBRATION COMPLETE! ===");
              Serial.println("You should hear ESC confirmation beeps.");
              Serial.println("Move AUX1 switch to LOW to exit calibration mode.");
              escCalibrationMode = false;
            }
            break;
        }
      }
    }
    
    // Exit calibration mode when AUX1 goes low
    if (escCalibrationMode && aux1 < 1200) {
      escCalibrationMode = false;
      calibrationStep = 0;
      Serial.println("Exiting ESC calibration mode...");
      
      // Ensure motors are at minimum
      for (int i = 0; i < 4; i++) {
        setMotorSpeed(i, 1000);
      }
    }
    
    // NORMAL ARMING SEQUENCE (only if not in calibration mode)
    if (!escCalibrationMode) {
      // Simple arming test: throttle low + yaw right for 2 seconds
      static uint32_t armStart = 0;
      static bool armSequence = false;
      
      if (!systemState.armed && throttle < 1100 && yaw > 1800) {
        if (!armSequence) {
          armStart = millis();
          armSequence = true;
          Serial.println("ARM SEQUENCE: Hold sticks for 2 seconds...");
        } else if (millis() - armStart > 2000) {
          systemState.armed = true;
          systemState.flightMode = FLIGHT_MODE_MANUAL;
          Serial.println("*** ARMED! ***");
          armSequence = false;
        }
      } else {
        armSequence = false;
      }
      
      // Disarm: throttle low + yaw left
      if (systemState.armed && throttle < 1100 && yaw < 1200) {
        systemState.armed = false;
        systemState.flightMode = FLIGHT_MODE_DISARMED;
        Serial.println("*** DISARMED! ***");
        
        // Set all motors to minimum when disarming
        for (int i = 0; i < 4; i++) {
          systemMotors.motor[i] = 1000;
          setMotorSpeed(i, 1000);
        }
        systemMotors.motorsArmed = false;
      }
      
      // FLIGHT MODES: Real flight control when armed
      if (systemState.armed) {
        // Get stick inputs (normalized to -1.0 to +1.0)
        float throttleInput = map(throttle, 1000, 2000, 0, 1000) / 1000.0f; // 0.0 to 1.0
        float rollInput = (systemReceiver.channels[RC_CHANNEL_ROLL] - 1500) / 500.0f;    // -1.0 to +1.0
        float pitchInput = (systemReceiver.channels[RC_CHANNEL_PITCH] - 1500) / 500.0f;  // -1.0 to +1.0
        float yawInput = (systemReceiver.channels[RC_CHANNEL_YAW] - 1500) / 500.0f;      // -1.0 to +1.0
        
        // Constrain inputs
        throttleInput = constrain(throttleInput, 0.0f, 1.0f);
        rollInput = constrain(rollInput, -1.0f, 1.0f);
        pitchInput = constrain(pitchInput, -1.0f, 1.0f);
        yawInput = constrain(yawInput, -1.0f, 1.0f);
        
        // Flight mode selection based on AUX1
        uint16_t modeSwitch = systemReceiver.channels[RC_CHANNEL_AUX1];
        if (modeSwitch < 1300) {
          systemState.flightMode = FLIGHT_MODE_MANUAL;
        } else if (modeSwitch < 1700) {
          systemState.flightMode = FLIGHT_MODE_STABILIZE;
        } else {
          systemState.flightMode = FLIGHT_MODE_ALT_HOLD;
        }
        
        // Execute flight mode
        switch (systemState.flightMode) {
          case FLIGHT_MODE_MANUAL:
            executeManualMode(throttleInput, rollInput, pitchInput, yawInput);
            break;
            
          case FLIGHT_MODE_STABILIZE:
            executeStabilizeMode(throttleInput, rollInput, pitchInput, yawInput);
            break;
            
          case FLIGHT_MODE_ALT_HOLD:
            executeAltHoldMode(throttleInput, rollInput, pitchInput, yawInput);
            break;
            
          default:
            executeManualMode(throttleInput, rollInput, pitchInput, yawInput);
            break;
        }
        
        // Debug output for flight modes
        static uint32_t lastFlightDebug = 0;
        if (millis() - lastFlightDebug > 2000) { // Every 2 seconds
          Serial.print("FLIGHT MODE: ");
          Serial.print(systemState.flightMode);
          Serial.print(", THR: ");
          Serial.print(throttleInput, 2);
          Serial.print(", R: ");
          Serial.print(rollInput, 2);
          Serial.print(", P: ");
          Serial.print(pitchInput, 2);
          Serial.print(", Y: ");
          Serial.println(yawInput, 2);
          lastFlightDebug = millis();
        }
      }
    }
    lastArmCheck = millis();
  }
  
  if (millis() - lastStatusUpdate > 1000) { // Every 1 second for debugging
    Serial.print("System Status - Mode: ");
    Serial.print(systemState.flightMode);
    Serial.print(", Armed: ");
    Serial.print(systemState.armed ? "YES" : "NO");
    
    // Add receiver channel debugging
    Serial.print(", THR: ");
    Serial.print(systemReceiver.channels[RC_CHANNEL_THROTTLE]);
    Serial.print(", YAW: ");
    Serial.print(systemReceiver.channels[RC_CHANNEL_YAW]);
    Serial.print(", AUX1: ");
    Serial.print(systemReceiver.channels[RC_CHANNEL_AUX1]);
    
    // Add motor output debugging
    Serial.print(", Motors: [");
    Serial.print(systemMotors.motor[0]);
    Serial.print(",");
    Serial.print(systemMotors.motor[1]);
    Serial.print(",");
    Serial.print(systemMotors.motor[2]);
    Serial.print(",");
    Serial.print(systemMotors.motor[3]);
    Serial.print("]");
    
    Serial.print(", Health: 0x");
    Serial.println(systemState.systemHealth, HEX);
    
    lastStatusUpdate = millis();
  }
  
  delay(100);
}

// Initialize hardware pins and peripherals
void initializeHardware() {
  // Configure LED pins
  pinMode(LED_BUILTIN, OUTPUT);
  pinMode(STATUS_LED_PIN, OUTPUT);
  pinMode(ERROR_LED_PIN, OUTPUT);
  
  // Configure buzzer
  pinMode(BUZZER_PIN, OUTPUT);
  
  // Initialize I2C for sensors
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000); // 400kHz fast mode
  
  // Initialize serial ports
  Serial1.begin(GPS_BAUD_RATE, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);

  
  Serial.println("Hardware initialization complete.");
}

// Main sensor reading and processing task
void sensorTask(void *pvParameters) {
  TickType_t xLastWakeTime = xTaskGetTickCount();
  
  while (1) {
    // Read all sensors
    readSensors(&systemSensors);
    
    // Update attitude estimation
    updateAttitudeEstimation(&systemSensors, &systemAttitude);
    
    // Update altitude estimation
    updateAltitudeEstimation(&systemSensors, &systemAltitude);
    
    vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(SENSOR_UPDATE_RATE_MS));
  }
}

// Main flight control task
void controlTask(void *pvParameters) {
  TickType_t xLastWakeTime = xTaskGetTickCount();
  
  // Create a composite flight data structure
  static FlightData_t flightData;
  
  while (1) {
    // Read receiver inputs
    readReceiverInputs(&systemReceiver);
    
    // Update composite flight data structure
    flightData.sensors = systemSensors;
    flightData.attitude = systemAttitude;
    flightData.gps = systemGPS;
    flightData.receiver = systemReceiver;
    flightData.motors = systemMotors;
    flightData.battery = systemBattery;
    flightData.altitude = systemAltitude;
    
    // Process flight mode transitions
    processFlightModeTransitions(&systemState, &flightData);
    
    // Execute current flight mode
    executeFlightMode(&systemState, &flightData);
    
    // Update motor outputs
    updateMotorOutputs(&systemMotors);
    
    vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(CONTROL_UPDATE_RATE_MS));
  }
}

// Safety monitoring task
void safetyTask(void *pvParameters) {
  TickType_t xLastWakeTime = xTaskGetTickCount();
  
  // Create a composite flight data structure
  static FlightData_t flightData;
  
  while (1) {
    // Update composite flight data structure
    flightData.sensors = systemSensors;
    flightData.attitude = systemAttitude;
    flightData.gps = systemGPS;
    flightData.receiver = systemReceiver;
    flightData.motors = systemMotors;
    flightData.battery = systemBattery;
    flightData.altitude = systemAltitude;
    
    // Update system health
    updateSystemHealth(&systemState);
    
    // Check safety systems
    checkSafetySystems(&systemState, &flightData);
    
    // Update battery monitoring
    updateBatteryMonitoring(&systemBattery);
    
    // Update status LEDs
    updateStatusLEDs(&systemState);
    
    // Monitor task stacks
    monitorTaskStacks();
    
    vTaskDelayUntil(&xLastWakeTime, pdMS_TO_TICKS(SAFETY_UPDATE_RATE_MS));
  }
}


// Update status LEDs based on system state
void updateStatusLEDs(SystemState_t *state) {
  static uint32_t lastBlink = 0;
  static bool ledState = false;
  uint32_t currentTime = millis();
  
  // Blink pattern based on system state
  uint16_t blinkRate = 1000; // Default slow blink
  
  if (state->armed) {
    blinkRate = 100; // Fast blink when armed
  } else if (state->emergencyMode) {
    blinkRate = 50;  // Very fast blink in emergency
  } else if (!state->receiverConnected) {
    blinkRate = 250; // Medium blink for no receiver
  }
  
  if (currentTime - lastBlink > blinkRate) {
    ledState = !ledState;
    digitalWrite(LED_BUILTIN, ledState);
    lastBlink = currentTime;
  }
}

// Stack monitoring function
void monitorTaskStacks(void) {
  static uint32_t lastStackCheck = 0;
  uint32_t currentTime = millis();
  
  if (currentTime - lastStackCheck > 10000) { // Check every 10 seconds
    lastStackCheck = currentTime;
    
    Serial.println("=== Task Stack Usage ===");
    
    if (sensorTaskHandle) {
      UBaseType_t sensorStack = uxTaskGetStackHighWaterMark(sensorTaskHandle);
      Serial.print("Sensor Task Stack Free: ");
      Serial.println(sensorStack * 4);
    }
    
    if (controlTaskHandle) {
      UBaseType_t controlStack = uxTaskGetStackHighWaterMark(controlTaskHandle);
      Serial.print("Control Task Stack Free: ");
      Serial.println(controlStack * 4);
    }
    
    if (safetyTaskHandle) {
      UBaseType_t safetyStack = uxTaskGetStackHighWaterMark(safetyTaskHandle);
      Serial.print("Safety Task Stack Free: ");
      Serial.println(safetyStack * 4);
    }
    
    Serial.print("Free Heap: ");
    Serial.println(ESP.getFreeHeap());
    Serial.println("========================");
  }
}

// Flight Mode Implementation Functions

// Manual Mode: Direct stick control, no stabilization
void executeManualMode(float throttle, float roll, float pitch, float yaw) {
  // Base throttle (1000-1800 range for safety)
  uint16_t baseThrottle = map(throttle * 1000, 0, 1000, 1000, 1800);
  
  // Manual control adjustments (limited for safety)
  int16_t rollAdjust = roll * 100;    // ±100 PWM units
  int16_t pitchAdjust = pitch * 100;  // ±100 PWM units
  int16_t yawAdjust = yaw * 50;       // ±50 PWM units for yaw
  
  // Calculate individual motor outputs
  // Standard quadcopter motor mixing
  systemMotors.motor[0] = constrain(baseThrottle - pitchAdjust - rollAdjust + yawAdjust, 1000, 1800); // Front Right
  systemMotors.motor[1] = constrain(baseThrottle - pitchAdjust + rollAdjust - yawAdjust, 1000, 1800); // Front Left  
  systemMotors.motor[2] = constrain(baseThrottle + pitchAdjust + rollAdjust + yawAdjust, 1000, 1800); // Rear Right
  systemMotors.motor[3] = constrain(baseThrottle + pitchAdjust - rollAdjust - yawAdjust, 1000, 1800); // Rear Left
  
  // Apply motor outputs
  for (int i = 0; i < 4; i++) {
    setMotorSpeed(i, systemMotors.motor[i]);
  }
  systemMotors.motorsArmed = true;
}

// Stabilize Mode: Attitude stabilization using gyro feedback
void executeStabilizeMode(float throttle, float roll, float pitch, float yaw) {
  // Base throttle
  uint16_t baseThrottle = map(throttle * 1000, 0, 1000, 1000, 1800);
  
  // Target angles from stick inputs (degrees)
  float targetRoll = roll * 30.0f;    // ±30 degrees max
  float targetPitch = pitch * 30.0f;  // ±30 degrees max
  float targetYawRate = yaw * 180.0f; // ±180 deg/s max
  
  // Current attitude (convert from radians to degrees)
  float currentRoll = systemAttitude.roll * RAD_TO_DEG;
  float currentPitch = systemAttitude.pitch * RAD_TO_DEG;
  float currentYawRate = systemAttitude.yawRate * RAD_TO_DEG;
  
  // Simple P controller for attitude
  float rollError = targetRoll - currentRoll;
  float pitchError = targetPitch - currentPitch;
  float yawError = targetYawRate - currentYawRate;
  
  // PID gains (conservative for safety)
  float kp = 2.0f;
  float rollAdjust = rollError * kp;
  float pitchAdjust = pitchError * kp;
  float yawAdjust = yawError * 0.5f;
  
  // Limit corrections
  rollAdjust = constrain(rollAdjust, -150.0f, 150.0f);
  pitchAdjust = constrain(pitchAdjust, -150.0f, 150.0f);
  yawAdjust = constrain(yawAdjust, -100.0f, 100.0f);
  
  // Motor mixing with stabilization
  systemMotors.motor[0] = constrain(baseThrottle - pitchAdjust - rollAdjust + yawAdjust, 1000, 1800); // Front Right
  systemMotors.motor[1] = constrain(baseThrottle - pitchAdjust + rollAdjust - yawAdjust, 1000, 1800); // Front Left
  systemMotors.motor[2] = constrain(baseThrottle + pitchAdjust + rollAdjust + yawAdjust, 1000, 1800); // Rear Right
  systemMotors.motor[3] = constrain(baseThrottle + pitchAdjust - rollAdjust - yawAdjust, 1000, 1800); // Rear Left
  
  // Apply motor outputs
  for (int i = 0; i < 4; i++) {
    setMotorSpeed(i, systemMotors.motor[i]);
  }
  systemMotors.motorsArmed = true;
}

// Altitude Hold Mode: Stabilize + altitude hold using barometer
void executeAltHoldMode(float throttle, float roll, float pitch, float yaw) {
  // For now, just use stabilize mode (altitude hold requires tuning)
  executeStabilizeMode(throttle, roll, pitch, yaw);
  
  // TODO: Add barometer-based altitude hold
  // This would use systemAltitude.altitude and systemAltitude.verticalSpeed
}