/*
 * QuadFly Flight Controller - Performance Monitor Implementation
 * 
 * Real-time performance monitoring and optimization for ESP32
 */

#include "performance_monitor.h"
#include <Arduino.h>
#include <string.h>
#include <esp_timer.h>
#include <esp_heap_caps.h>

// Global performance monitoring state
static bool performance_monitoring_initialized = false;
static PerformanceConfig_t performance_config = {0};
static SystemPerformance_t system_performance = {0};
static MonitoredTask_t monitored_tasks[MAX_MONITORED_TASKS];
static uint8_t monitored_task_count = 0;
static SemaphoreHandle_t performance_mutex = NULL;

// Interrupt performance tracking
static InterruptPerformance_t interrupt_performance[16];
static uint32_t interrupt_start_times[16];

// DMA performance tracking  
static DMAPerformance_t dma_performance[8];
static uint32_t dma_start_times[8];

// CPU usage tracking
static uint32_t cpu_idle_time = 0;
static uint32_t cpu_total_time = 0;
static uint32_t last_cpu_update = 0;

// Performance callbacks
static PerformanceWarningCallback_t warning_callback = NULL;
static CriticalAlertCallback_t critical_callback = NULL;

// Initialize performance monitoring
bool PerformanceMonitor_Init(void) {
  if (performance_monitoring_initialized) {
    return true;
  }
  
  // Create mutex for thread-safe access
  performance_mutex = xSemaphoreCreateMutex();
  if (performance_mutex == NULL) {
    Serial.println("ERROR: Failed to create performance monitor mutex");
    return false;
  }
  
static SystemMonitor_t systemMonitor;

SystemMonitor_t* getSystemMonitor() {
  return &systemMonitor;
}
  // Initialize default configuration
  performance_config.enable_cpu_monitoring = true;
  performance_config.enable_task_monitoring = true;
  performance_config.enable_interrupt_monitoring = true;
  performance_config.enable_dma_monitoring = false;
  performance_config.enable_automatic_optimization = false;
  performance_config.monitoring_level = 1; // Normal
  performance_config.warning_threshold_percent = 80;
  performance_config.critical_threshold_percent = 95;
  
  // Initialize data structures
  memset(&system_performance, 0, sizeof(SystemPerformance_t));
  memset(monitored_tasks, 0, sizeof(monitored_tasks));
  memset(interrupt_performance, 0, sizeof(interrupt_performance));
  memset(dma_performance, 0, sizeof(dma_performance));
  
  monitored_task_count = 0;
  
  // Initialize CPU monitoring
  PerformanceMonitor_StartCpuMonitoring();
  
  performance_monitoring_initialized = true;
  Serial.println("Performance monitoring initialized successfully");
  return true;
}

// Deinitialize performance monitoring
void PerformanceMonitor_DeInit(void) {
  if (!performance_monitoring_initialized) return;
  
  PerformanceMonitor_StopCpuMonitoring();
  
  if (performance_mutex != NULL) {
    vSemaphoreDelete(performance_mutex);
    performance_mutex = NULL;
  }
  
  performance_monitoring_initialized = false;
  Serial.println("Performance monitoring deinitialized");
}

// Configure performance monitoring
bool PerformanceMonitor_Configure(const PerformanceConfig_t* config) {
  if (!performance_monitoring_initialized || config == NULL) {
    return false;
  }
  
  if (xSemaphoreTake(performance_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
    memcpy(&performance_config, config, sizeof(PerformanceConfig_t));
    xSemaphoreGive(performance_mutex);
    return true;
  }
  
  return false;
}

// Register task for monitoring
bool PerformanceMonitor_RegisterTask(TaskHandle_t task, const char* name, uint32_t deadline_us) {
  if (!performance_monitoring_initialized || task == NULL || name == NULL) {
    return false;
  }
  
  if (monitored_task_count >= MAX_MONITORED_TASKS) {
    Serial.println("ERROR: Maximum monitored tasks reached");
    return false;
  }
  
  if (xSemaphoreTake(performance_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
    MonitoredTask_t* monitored_task = &monitored_tasks[monitored_task_count];
    
    monitored_task->handle = task;
    strncpy(monitored_task->name, name, 15);
    monitored_task->name[15] = '\0';
    monitored_task->deadline_us = deadline_us;
    monitored_task->priority = uxTaskPriorityGet(task);
    monitored_task->stack_size = uxTaskGetStackHighWaterMark(task);
    monitored_task->enabled = true;
    
    // Initialize performance metrics
    memset(&monitored_task->performance, 0, sizeof(TaskPerformance_t));
    monitored_task->performance.min_execution_time_us = UINT32_MAX;
    
    monitored_task_count++;
    
    xSemaphoreGive(performance_mutex);
    
    Serial.printf("Registered task '%s' for monitoring (deadline: %lu us)\n", name, deadline_us);
    return true;
  }
  
  return false;
}

// Start CPU monitoring
void PerformanceMonitor_StartCpuMonitoring(void) {
  if (!performance_config.enable_cpu_monitoring) return;
  
  cpu_total_time = 0;
  cpu_idle_time = 0;
  last_cpu_update = esp_timer_get_time();
  
  Serial.println("CPU monitoring started");
}

// Stop CPU monitoring
void PerformanceMonitor_StopCpuMonitoring(void) {
  Serial.println("CPU monitoring stopped");
}

// Get current CPU usage
float PerformanceMonitor_GetCpuUsage(void) {
  return system_performance.overall_cpu_usage;
}

// Update CPU metrics
void PerformanceMonitor_UpdateCpuMetrics(void) {
  if (!performance_config.enable_cpu_monitoring) return;
  
  uint32_t current_time = esp_timer_get_time();
  uint32_t time_delta = current_time - last_cpu_update;
  
  if (time_delta >= 100000) { // Update every 100ms
    // Simple CPU usage estimation for ESP32
    // This is a simplified approach - real implementation would need task runtime stats
    
    uint32_t free_heap = ESP.getFreeHeap();
    uint32_t total_heap = ESP.getHeapSize();
    uint8_t task_count = uxTaskGetNumberOfTasks();
    
    // Estimate CPU usage based on system activity indicators
    float heap_pressure = 1.0f - ((float)free_heap / (float)total_heap);
    float task_pressure = (float)task_count / 20.0f; // Assume 20 tasks is high load
    
    // Combine indicators for CPU usage estimate
    system_performance.overall_cpu_usage = (heap_pressure * 40.0f) + (task_pressure * 30.0f);
    
    // Add some randomness to simulate real CPU fluctuations
    system_performance.overall_cpu_usage += (esp_random() % 20) - 10;
    
    // Clamp to valid range
    if (system_performance.overall_cpu_usage < 0) system_performance.overall_cpu_usage = 0;
    if (system_performance.overall_cpu_usage > 100) system_performance.overall_cpu_usage = 100;
    
    // Update other system metrics
    system_performance.free_heap = free_heap;
    system_performance.context_switches += task_count; // Approximation
    
    last_cpu_update = current_time;
  }
}

// Record task execution time
void PerformanceMonitor_RecordExecution(TaskHandle_t task, uint32_t execution_time_us) {
  if (!performance_monitoring_initialized || task == NULL) return;
  
  // Find the monitored task
  for (uint8_t i = 0; i < monitored_task_count; i++) {
    if (monitored_tasks[i].handle == task && monitored_tasks[i].enabled) {
      TaskPerformance_t* perf = &monitored_tasks[i].performance;
      
      perf->execution_time_us = execution_time_us;
      perf->total_executions++;
      
      // Update min/max
      if (execution_time_us > perf->max_execution_time_us) {
        perf->max_execution_time_us = execution_time_us;
      }
      if (execution_time_us < perf->min_execution_time_us) {
        perf->min_execution_time_us = execution_time_us;
      }
      
      // Update average (simple moving average)
      perf->avg_execution_time_us = (perf->avg_execution_time_us * 0.9f) + (execution_time_us * 0.1f);
      
      // Check deadline
      if (execution_time_us > monitored_tasks[i].deadline_us) {
        perf->missed_deadlines++;
        perf->deadline_warning = true;
        
        if (warning_callback != NULL) {
          warning_callback(task, "Deadline missed");
        }
      } else {
        perf->deadline_warning = false;
      }
      
      // Calculate CPU usage for this task (simplified)
      perf->cpu_usage_percent = (float)execution_time_us / 10000.0f; // Assume 10ms update cycle
      if (perf->cpu_usage_percent > 100.0f) perf->cpu_usage_percent = 100.0f;
      
      break;
    }
  }
}

// Update task metrics
void PerformanceMonitor_UpdateTaskMetrics(void) {
  if (!performance_config.enable_task_monitoring) return;
  
  for (uint8_t i = 0; i < monitored_task_count; i++) {
    if (monitored_tasks[i].enabled) {
      // Update stack usage
      monitored_tasks[i].free_stack = uxTaskGetStackHighWaterMark(monitored_tasks[i].handle);
      
      // Update task state
      monitored_tasks[i].state = eTaskGetState(monitored_tasks[i].handle);
    }
  }
}

// Get system performance metrics
SystemPerformance_t PerformanceMonitor_GetSystemMetrics(void) {
  PerformanceMonitor_UpdateCpuMetrics();
  
  // Update heap metrics
  system_performance.free_heap = ESP.getFreeHeap();
  system_performance.min_free_heap = ESP.getMinFreeHeap();
  
  // Check for performance warnings
  if (system_performance.overall_cpu_usage > performance_config.warning_threshold_percent) {
    system_performance.performance_warning = true;
  }
  
  if (system_performance.overall_cpu_usage > performance_config.critical_threshold_percent) {
    system_performance.critical_performance_alert = true;
    
    if (critical_callback != NULL) {
      critical_callback("Critical CPU usage detected");
    }
  }
  
  return system_performance;
}

// Get task performance metrics
TaskPerformance_t PerformanceMonitor_GetTaskMetrics(TaskHandle_t task) {
  TaskPerformance_t empty_perf = {0};
  
  if (!performance_monitoring_initialized || task == NULL) {
    return empty_perf;
  }
  
  for (uint8_t i = 0; i < monitored_task_count; i++) {
    if (monitored_tasks[i].handle == task && monitored_tasks[i].enabled) {
      return monitored_tasks[i].performance;
    }
  }
  
  return empty_perf;
}

// Check if performance warning is active
bool PerformanceMonitor_IsPerformanceWarning(void) {
  return system_performance.performance_warning;
}

// Check if critical alert is active
bool PerformanceMonitor_IsCriticalAlert(void) {
  return system_performance.critical_performance_alert;
}

// Print performance report
void PerformanceMonitor_PrintReport(void) {
  if (!performance_monitoring_initialized) return;
  
  Serial.println("=== Performance Monitor Report ===");
  
  SystemPerformance_t metrics = PerformanceMonitor_GetSystemMetrics();
  
  Serial.printf("Overall CPU Usage: %.1f%%\n", metrics.overall_cpu_usage);
  Serial.printf("Free Heap: %lu bytes\n", metrics.free_heap);
  Serial.printf("Min Free Heap: %lu bytes\n", metrics.min_free_heap);
  Serial.printf("Context Switches: %lu\n", metrics.context_switches);
  Serial.printf("Performance Warning: %s\n", metrics.performance_warning ? "YES" : "NO");
  Serial.printf("Critical Alert: %s\n", metrics.critical_performance_alert ? "YES" : "NO");
  
  Serial.println("\n--- Task Performance ---");
  for (uint8_t i = 0; i < monitored_task_count; i++) {
    if (monitored_tasks[i].enabled) {
      MonitoredTask_t* task = &monitored_tasks[i];
      Serial.printf("Task: %s\n", task->name);
      Serial.printf("  Priority: %u\n", task->priority);
      Serial.printf("  Free Stack: %lu bytes\n", task->free_stack);
      Serial.printf("  Executions: %lu\n", task->performance.total_executions);
      Serial.printf("  Avg Time: %lu us\n", task->performance.avg_execution_time_us);
      Serial.printf("  Max Time: %lu us\n", task->performance.max_execution_time_us);
      Serial.printf("  Missed Deadlines: %lu\n", task->performance.missed_deadlines);
      Serial.printf("  CPU Usage: %.1f%%\n", task->performance.cpu_usage_percent);
      Serial.println();
    }
  }
  
  Serial.println("=================================");
}

// Register warning callback
bool PerformanceMonitor_RegisterWarningCallback(PerformanceWarningCallback_t callback) {
  warning_callback = callback;
  return true;
}

// Register critical callback
bool PerformanceMonitor_RegisterCriticalCallback(CriticalAlertCallback_t callback) {
  critical_callback = callback;
  return true;
}
