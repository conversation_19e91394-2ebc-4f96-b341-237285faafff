/*
 * QuadFly Flight Controller - In-Flight Safety Enhancements Header
 * 
 * Wind estimation, GPS recovery, emergency landing, and battery cell monitoring
 */

#ifndef INFLIGHT_SAFETY_H
#define INFLIGHT_SAFETY_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include "math_utils.h"

// Wind estimation data
typedef struct {
  Vector3_t wind_velocity;      // Wind velocity in NED frame (m/s)
  float wind_speed;             // Wind speed magnitude (m/s)
  float wind_direction;         // Wind direction (degrees from north)
  float wind_vertical;          // Vertical wind component (m/s)
  float confidence;             // Estimation confidence (0-1)
  uint32_t last_update;         // Last update timestamp
  bool valid;                   // Estimation validity
  
  // Wind statistics
  float avg_wind_speed;
  float max_wind_speed;
  float wind_variability;
  uint32_t estimation_count;
  
  // Turbulence detection
  float turbulence_intensity;
  bool severe_turbulence;
  uint32_t turbulence_events;
  
} WindEstimation_t;

// GPS recovery system
typedef struct {
  bool gps_available;
  bool gps_lost;
  uint32_t gps_loss_time;
  uint32_t gps_recovery_time;
  
  // Last known GPS position
  Vector3_t last_gps_position;
  Vector3_t last_gps_velocity;
  uint32_t last_gps_timestamp;
  
  // Dead reckoning navigation
  Vector3_t estimated_position;
  Vector3_t estimated_velocity;
  float position_uncertainty;
  float velocity_uncertainty;
  
  // Recovery strategies
  bool using_dead_reckoning;
  bool using_optical_flow;
  bool using_visual_odometry;
  bool using_inertial_navigation;
  
  // Recovery configuration
  uint32_t max_gps_loss_time_ms;
  float max_position_uncertainty_m;
  bool auto_land_on_gps_loss;
  bool return_to_last_known_position;
  
} GPSRecovery_t;

// Emergency landing system
typedef struct {
  bool landing_active;
  bool emergency_landing;
  uint32_t landing_start_time;
  
  // Landing phases
  typedef enum {
    LANDING_PHASE_NONE = 0,
    LANDING_PHASE_DESCENT = 1,
    LANDING_PHASE_APPROACH = 2,
    LANDING_PHASE_TOUCHDOWN = 3,
    LANDING_PHASE_GROUND = 4
  } LandingPhase_t;
  
  LandingPhase_t current_phase;
  
  // Landing parameters
  float descent_rate;           // m/s
  float approach_altitude;      // m
  float touchdown_threshold;    // m
  float ground_detection_threshold; // m/s
  
  // Landing site selection
  Vector3_t landing_site;
  float landing_site_score;
  bool landing_site_valid;
  
  // Obstacle avoidance during landing
  bool obstacles_detected;
  Vector3_t obstacle_positions[8];
  uint8_t obstacle_count;
  
  // Landing safety
  float max_descent_rate;
  float min_landing_altitude;
  bool abort_landing;
  uint32_t landing_timeout_ms;
  
} EmergencyLanding_t;

// Battery cell monitoring
typedef struct {
  uint8_t cell_count;
  float cell_voltages[8];       // Individual cell voltages (V)
  float cell_temperatures[8];   // Individual cell temperatures (°C)
  float cell_resistances[8];    // Internal resistances (mΩ)
  
  // Cell health indicators
  float cell_capacity[8];       // Remaining capacity per cell (%)
  float cell_health[8];         // Health score per cell (0-100)
  bool cell_balanced;           // Battery pack balance status
  float voltage_imbalance;      // Maximum voltage difference (V)
  
  // Safety thresholds
  float min_cell_voltage;       // Minimum safe cell voltage (V)
  float max_cell_voltage;       // Maximum safe cell voltage (V)
  float max_cell_temperature;   // Maximum safe temperature (°C)
  float max_voltage_imbalance;  // Maximum allowed imbalance (V)
  
  // Monitoring status
  bool monitoring_active;
  uint32_t last_update;
  uint32_t monitoring_interval_ms;
  
  // Failure detection
  bool cell_failure_detected;
  uint8_t failed_cells;
  bool thermal_runaway_risk;
  bool overcharge_risk;
  bool overdischarge_risk;
  
} BatteryCellMonitoring_t;

// Flight envelope protection
typedef struct {
  // Angle of attack limits
  float max_angle_of_attack;
  float stall_angle;
  bool aoa_protection_active;
  
  // Speed limits
  float max_airspeed;
  float min_airspeed;
  float vne_speed;              // Never exceed speed
  bool speed_protection_active;
  
  // Load factor limits
  float max_positive_g;
  float max_negative_g;
  float current_load_factor;
  bool load_protection_active;
  
  // Altitude limits
  float service_ceiling;
  float absolute_ceiling;
  bool altitude_protection_active;
  
} FlightEnvelopeProtection_t;

// Vibration monitoring
typedef struct {
  Vector3_t vibration_levels;   // Current vibration levels (g)
  Vector3_t max_vibration;      // Maximum recorded vibration (g)
  Vector3_t avg_vibration;      // Average vibration levels (g)
  
  // Frequency analysis
  float dominant_frequency;     // Hz
  float vibration_spectrum[32]; // Frequency spectrum
  
  // Thresholds and alerts
  float warning_threshold;      // g
  float critical_threshold;     // g
  bool vibration_warning;
  bool vibration_critical;
  
  // Source identification
  bool motor_vibration[4];
  bool propeller_imbalance[4];
  bool structural_resonance;
  
} VibrationMonitoring_t;

// Function declarations

// Initialization
bool InFlightSafety_Init(void);
void InFlightSafety_DeInit(void);
void InFlightSafety_Update(void);

// Wind estimation and compensation
bool InFlightSafety_InitWindEstimation(void);
void InFlightSafety_UpdateWindEstimation(Vector3_t ground_velocity, Vector3_t air_velocity);
WindEstimation_t InFlightSafety_GetWindEstimation(void);
Vector3_t InFlightSafety_CompensateWind(Vector3_t desired_velocity);
bool InFlightSafety_IsWindSafeForFlight(void);
void InFlightSafety_AdaptControlToWind(float* pid_gains);

// GPS recovery system
bool InFlightSafety_InitGPSRecovery(void);
void InFlightSafety_UpdateGPSStatus(bool gps_available, Vector3_t position, Vector3_t velocity);
void InFlightSafety_HandleGPSLoss(void);
Vector3_t InFlightSafety_GetEstimatedPosition(void);
Vector3_t InFlightSafety_GetEstimatedVelocity(void);
float InFlightSafety_GetPositionUncertainty(void);
bool InFlightSafety_IsGPSRecoveryActive(void);

// Dead reckoning navigation
void InFlightSafety_UpdateDeadReckoning(Vector3_t acceleration, Vector3_t angular_velocity, float dt);
void InFlightSafety_ResetDeadReckoning(Vector3_t known_position, Vector3_t known_velocity);
bool InFlightSafety_IsDeadReckoningReliable(void);

// Emergency landing system
bool InFlightSafety_InitEmergencyLanding(void);
bool InFlightSafety_StartEmergencyLanding(void);
void InFlightSafety_UpdateEmergencyLanding(void);
bool InFlightSafety_SelectLandingSite(Vector3_t current_position);
bool InFlightSafety_IsLandingSiteValid(Vector3_t site);
void InFlightSafety_AbortLanding(void);
bool InFlightSafety_IsEmergencyLandingActive(void);

// Landing site analysis
float InFlightSafety_EvaluateLandingSite(Vector3_t site);
bool InFlightSafety_DetectObstacles(Vector3_t position, float radius);
Vector3_t InFlightSafety_FindSafestLandingSite(Vector3_t search_center, float search_radius);

// Battery cell monitoring
bool InFlightSafety_InitBatteryCellMonitoring(uint8_t cell_count);
void InFlightSafety_UpdateCellVoltages(const float* voltages);
void InFlightSafety_UpdateCellTemperatures(const float* temperatures);
BatteryCellMonitoring_t InFlightSafety_GetBatteryStatus(void);
bool InFlightSafety_IsBatterySafe(void);
bool InFlightSafety_DetectCellFailure(void);
uint8_t InFlightSafety_GetFailedCells(void);

// Flight envelope protection
bool InFlightSafety_InitFlightEnvelopeProtection(void);
void InFlightSafety_UpdateFlightEnvelope(Vector3_t velocity, Vector3_t attitude, float altitude);
bool InFlightSafety_CheckFlightEnvelope(void);
Vector3_t InFlightSafety_LimitFlightEnvelope(Vector3_t desired_velocity);
bool InFlightSafety_IsWithinFlightEnvelope(void);

// Vibration monitoring
bool InFlightSafety_InitVibrationMonitoring(void);
void InFlightSafety_UpdateVibrationData(Vector3_t acceleration);
VibrationMonitoring_t InFlightSafety_GetVibrationStatus(void);
bool InFlightSafety_DetectExcessiveVibration(void);
void InFlightSafety_AnalyzeVibrationSource(void);

// Turbulence detection and handling
bool InFlightSafety_DetectTurbulence(void);
float InFlightSafety_GetTurbulenceIntensity(void);
void InFlightSafety_AdaptToTurbulence(void);
bool InFlightSafety_IsSevereTurbulence(void);

// Icing detection and mitigation
bool InFlightSafety_DetectIcing(void);
void InFlightSafety_HandleIcing(void);
bool InFlightSafety_IsIcingConditions(float temperature, float humidity);

// System health monitoring
typedef struct {
  bool motor_health[4];
  bool esc_health[4];
  bool sensor_health[8];
  bool communication_health;
  bool power_system_health;
  bool flight_controller_health;
  float overall_health_score;
} SystemHealth_t;

SystemHealth_t InFlightSafety_GetSystemHealth(void);
void InFlightSafety_UpdateSystemHealth(void);
bool InFlightSafety_IsSystemHealthy(void);

// Emergency procedures
void InFlightSafety_ExecuteEmergencyProcedure(uint8_t procedure_id);
bool InFlightSafety_CanContinueFlight(void);
void InFlightSafety_InitiateControlledCrash(void);
void InFlightSafety_PrepareForImpact(void);

// Data logging for safety analysis
void InFlightSafety_LogSafetyEvent(const char* event, uint8_t severity);
void InFlightSafety_LogFlightData(void);
bool InFlightSafety_SaveFlightLog(void);

// Configuration and calibration
typedef struct {
  float wind_estimation_gain;
  uint32_t gps_loss_timeout_ms;
  float emergency_descent_rate;
  float cell_voltage_threshold;
  float vibration_threshold;
  bool enable_envelope_protection;
  bool enable_turbulence_adaptation;
} InFlightSafetyConfig_t;

bool InFlightSafety_Configure(const InFlightSafetyConfig_t* config);
InFlightSafetyConfig_t InFlightSafety_GetConfiguration(void);

// Diagnostic and testing
void InFlightSafety_RunSelfTest(void);
bool InFlightSafety_TestWindEstimation(void);
bool InFlightSafety_TestGPSRecovery(void);
bool InFlightSafety_TestEmergencyLanding(void);
void InFlightSafety_PrintDiagnostics(void);

// Error handling
typedef enum {
  INFLIGHT_ERROR_NONE = 0,
  INFLIGHT_ERROR_WIND_ESTIMATION_FAILED,
  INFLIGHT_ERROR_GPS_RECOVERY_FAILED,
  INFLIGHT_ERROR_LANDING_SITE_NOT_FOUND,
  INFLIGHT_ERROR_BATTERY_CELL_FAILURE,
  INFLIGHT_ERROR_EXCESSIVE_VIBRATION,
  INFLIGHT_ERROR_FLIGHT_ENVELOPE_VIOLATION,
  INFLIGHT_ERROR_SYSTEM_FAILURE
} InFlightError_t;

InFlightError_t InFlightSafety_GetLastError(void);
const char* InFlightSafety_GetErrorString(InFlightError_t error);

#endif // INFLIGHT_SAFETY_H
