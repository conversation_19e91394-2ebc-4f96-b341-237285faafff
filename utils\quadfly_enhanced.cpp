/*
 * QuadFly Flight Controller - Enhanced Systems Integration Implementation
 * 
 * Master implementation for all enhanced features and systems
 */

#include "quadfly_enhanced.h"
#include "system_utils.h"
#include "performance_monitor.h"
#include "enhanced_safety.h"
#include "config_manager.h"
#include <Arduino.h>
#include <string.h>

// Global enhanced system state
QuadFlyEnhancedCapabilities_t g_enhanced_capabilities = {0};
QuadFlyEnhancedConfig_t g_enhanced_config = {0};

// Internal state
static bool enhanced_system_initialized = false;
static uint32_t last_update_time = 0;
static QuadFlyEnhancedError_t last_error = ENHANCED_ERROR_NONE;

// System callbacks
static SystemEventCallback_t system_event_callback = NULL;
static PerformanceCallback_t performance_callback = NULL;
static SafetyCallback_t safety_callback = NULL;

// Feature enable flags
static bool feature_flags[16] = {false};

// Internal function declarations
static void updateCapabilities(void);
static void checkSystemHealth(void);
static void handleSystemEvent(const char* event, uint8_t severity);
static void handlePerformanceUpdate(float cpu_usage, uint32_t free_memory);
static void handleSafetyEvent(FailsafeTrigger_t trigger, FailsafeLevel_t level);

// Initialize enhanced QuadFly system
bool QuadFlyEnhanced_Init(void) {
  if (enhanced_system_initialized) {
    return true;
  }
  
  Serial.println("Initializing QuadFly Enhanced Systems...");
  Serial.printf("Version: %s\n", QUADFLY_ENHANCED_VERSION);
  Serial.printf("Build: %s %s\n", QUADFLY_ENHANCED_BUILD_DATE, QUADFLY_ENHANCED_BUILD_TIME);
  
  // Initialize default configuration
  memset(&g_enhanced_config, 0, sizeof(QuadFlyEnhancedConfig_t));
  g_enhanced_config.enable_enhanced_features = true;
  g_enhanced_config.system_performance_level = 1; // Normal
  g_enhanced_config.enable_performance_monitoring = true;
  g_enhanced_config.enable_enhanced_safety = true;
  g_enhanced_config.enable_inflight_safety = true;
  
  // Initialize capabilities
  memset(&g_enhanced_capabilities, 0, sizeof(QuadFlyEnhancedCapabilities_t));
  
  // Initialize core systems in order of dependency
  bool init_success = true;
  
  // 1. System utilities (foundation)
  initializeSystemMonitoring();
  g_enhanced_capabilities.hal_abstraction_active = true;
  
  // 2. Performance monitoring
  if (g_enhanced_config.enable_performance_monitoring) {
    if (PerformanceMonitor_Init()) {
      g_enhanced_capabilities.performance_monitoring_active = true;
      PerformanceMonitor_RegisterWarningCallback(handlePerformanceUpdate);
      Serial.println("✓ Performance monitoring initialized");
    } else {
      Serial.println("✗ Performance monitoring failed to initialize");
      init_success = false;
    }
  }
  
  // 3. Enhanced safety systems
  if (g_enhanced_config.enable_enhanced_safety) {
    if (EnhancedSafety_Init()) {
      g_enhanced_capabilities.enhanced_safety_active = true;
      EnhancedSafety_RegisterSafetyCallback(handleSafetyEvent);
      Serial.println("✓ Enhanced safety systems initialized");
    } else {
      Serial.println("✗ Enhanced safety systems failed to initialize");
      init_success = false;
    }
  }
  
  // 4. Configuration manager
  if (ConfigManager_Init()) {
    g_enhanced_capabilities.enhanced_config_available = true;
    Serial.println("✓ Enhanced configuration manager initialized");
  } else {
    Serial.println("✗ Enhanced configuration manager failed to initialize");
    init_success = false;
  }
  
  // 5. Memory management (optional)
  if (g_enhanced_config.enable_memory_management) {
    // Memory manager would be initialized here
    g_enhanced_capabilities.memory_management_active = false; // Not fully implemented
    Serial.println("○ Memory management (placeholder)");
  }
  
  // 6. Advanced sensor fusion (optional)
  if (g_enhanced_config.enable_advanced_sensor_fusion) {
    // Sensor fusion would be initialized here
    g_enhanced_capabilities.advanced_sensor_fusion_active = false; // Not fully implemented
    Serial.println("○ Advanced sensor fusion (placeholder)");
  }
  
  // 7. AI/ML engine (optional)
  if (g_enhanced_config.enable_ai_ml_engine) {
    // AI/ML engine would be initialized here
    g_enhanced_capabilities.ai_ml_engine_active = false; // Not fully implemented
    Serial.println("○ AI/ML engine (placeholder)");
  }
  
  // 8. In-flight safety (optional)
  if (g_enhanced_config.enable_inflight_safety) {
    // In-flight safety would be initialized here
    g_enhanced_capabilities.inflight_safety_active = false; // Not fully implemented
    Serial.println("○ In-flight safety (placeholder)");
  }
  
  if (init_success) {
    enhanced_system_initialized = true;
    last_update_time = millis();
    
    // Set initial capabilities
    updateCapabilities();
    
    Serial.println("✓ QuadFly Enhanced Systems initialized successfully!");
    Serial.printf("Active features: %d\n", 
                  g_enhanced_capabilities.performance_monitoring_active +
                  g_enhanced_capabilities.enhanced_safety_active +
                  g_enhanced_capabilities.enhanced_config_available);
    
    return true;
  } else {
    last_error = ENHANCED_ERROR_INITIALIZATION_FAILED;
    Serial.println("✗ QuadFly Enhanced Systems initialization failed!");
    return false;
  }
}

// Deinitialize enhanced system
void QuadFlyEnhanced_DeInit(void) {
  if (!enhanced_system_initialized) return;
  
  Serial.println("Deinitializing QuadFly Enhanced Systems...");
  
  // Deinitialize systems in reverse order
  PerformanceMonitor_DeInit();
  
  enhanced_system_initialized = false;
  memset(&g_enhanced_capabilities, 0, sizeof(QuadFlyEnhancedCapabilities_t));
  
  Serial.println("QuadFly Enhanced Systems deinitialized");
}

// Configure enhanced system
bool QuadFlyEnhanced_Configure(const QuadFlyEnhancedConfig_t* config) {
  if (!enhanced_system_initialized || config == NULL) {
    last_error = ENHANCED_ERROR_CONFIGURATION_INVALID;
    return false;
  }
  
  memcpy(&g_enhanced_config, config, sizeof(QuadFlyEnhancedConfig_t));
  
  // Apply configuration to subsystems
  if (g_enhanced_capabilities.performance_monitoring_active) {
    PerformanceMonitor_Configure(&config->performance_config);
  }
  
  if (g_enhanced_capabilities.enhanced_safety_active) {
    EnhancedSafety_Configure(&config->safety_limits);
  }
  
  Serial.println("Enhanced system configuration updated");
  return true;
}

// Update enhanced system
void QuadFlyEnhanced_Update(void) {
  if (!enhanced_system_initialized) return;
  
  uint32_t current_time = millis();
  
  // Update at 10Hz to avoid overwhelming the system
  if (current_time - last_update_time < 100) return;
  
  // Update subsystems
  if (g_enhanced_capabilities.performance_monitoring_active) {
    PerformanceMonitor_UpdateTaskMetrics();
  }
  
  if (g_enhanced_capabilities.enhanced_safety_active) {
    EnhancedSafety_UpdateFailsafeState();
  }
  
  // Update system capabilities
  updateCapabilities();
  
  // Check system health
  checkSystemHealth();
  
  last_update_time = current_time;
}

// Get system capabilities
QuadFlyEnhancedCapabilities_t QuadFlyEnhanced_GetCapabilities(void) {
  return g_enhanced_capabilities;
}

// Enable/disable feature
bool QuadFlyEnhanced_EnableFeature(const char* feature_name, bool enable) {
  if (!enhanced_system_initialized || feature_name == NULL) {
    return false;
  }
  
  // Simple feature flag system
  if (strcmp(feature_name, "performance_monitoring") == 0) {
    feature_flags[0] = enable;
    g_enhanced_config.enable_performance_monitoring = enable;
  } else if (strcmp(feature_name, "enhanced_safety") == 0) {
    feature_flags[1] = enable;
    g_enhanced_config.enable_enhanced_safety = enable;
  } else if (strcmp(feature_name, "logging") == 0) {
    feature_flags[2] = enable;
  } else {
    return false;
  }
  
  Serial.printf("Feature '%s' %s\n", feature_name, enable ? "enabled" : "disabled");
  return true;
}

// Check if feature is enabled
bool QuadFlyEnhanced_IsFeatureEnabled(const char* feature_name) {
  if (!enhanced_system_initialized || feature_name == NULL) {
    return false;
  }
  
  if (strcmp(feature_name, "performance_monitoring") == 0) {
    return feature_flags[0];
  } else if (strcmp(feature_name, "enhanced_safety") == 0) {
    return feature_flags[1];
  } else if (strcmp(feature_name, "logging") == 0) {
    return feature_flags[2];
  }
  
  return false;
}

// Get system health
float QuadFlyEnhanced_GetSystemHealth(void) {
  if (!enhanced_system_initialized) return 0.0f;
  
  float health_score = 100.0f;
  
  // Check performance metrics
  if (g_enhanced_capabilities.performance_monitoring_active) {
    SystemPerformance_t perf = PerformanceMonitor_GetSystemMetrics();
    
    if (perf.overall_cpu_usage > 90.0f) health_score -= 20.0f;
    else if (perf.overall_cpu_usage > 80.0f) health_score -= 10.0f;
    
    if (perf.performance_warning) health_score -= 15.0f;
    if (perf.critical_performance_alert) health_score -= 30.0f;
  }
  
  // Check safety status
  if (g_enhanced_capabilities.enhanced_safety_active) {
    FailsafeLevel_t level = EnhancedSafety_GetFailsafeLevel();
    
    switch (level) {
      case FAILSAFE_LEVEL_WARNING: health_score -= 10.0f; break;
      case FAILSAFE_LEVEL_CAUTION: health_score -= 20.0f; break;
      case FAILSAFE_LEVEL_CRITICAL: health_score -= 40.0f; break;
      case FAILSAFE_LEVEL_EMERGENCY: health_score -= 60.0f; break;
      case FAILSAFE_LEVEL_CATASTROPHIC: health_score -= 80.0f; break;
      default: break;
    }
  }
  
  // Clamp to valid range
  if (health_score < 0.0f) health_score = 0.0f;
  if (health_score > 100.0f) health_score = 100.0f;
  
  return health_score;
}

// Print system status
void QuadFlyEnhanced_PrintSystemStatus(void) {
  if (!enhanced_system_initialized) {
    Serial.println("Enhanced system not initialized");
    return;
  }
  
  Serial.println("=== QuadFly Enhanced System Status ===");
  Serial.printf("Version: %s\n", QUADFLY_ENHANCED_VERSION);
  Serial.printf("Uptime: %lu ms\n", millis());
  Serial.printf("System Health: %.1f%%\n", QuadFlyEnhanced_GetSystemHealth());
  
  Serial.println("\n--- Active Features ---");
  Serial.printf("Performance Monitoring: %s\n", g_enhanced_capabilities.performance_monitoring_active ? "ACTIVE" : "INACTIVE");
  Serial.printf("Enhanced Safety: %s\n", g_enhanced_capabilities.enhanced_safety_active ? "ACTIVE" : "INACTIVE");
  Serial.printf("Enhanced Config: %s\n", g_enhanced_capabilities.enhanced_config_available ? "AVAILABLE" : "UNAVAILABLE");
  Serial.printf("Memory Management: %s\n", g_enhanced_capabilities.memory_management_active ? "ACTIVE" : "INACTIVE");
  Serial.printf("Advanced Sensor Fusion: %s\n", g_enhanced_capabilities.advanced_sensor_fusion_active ? "ACTIVE" : "INACTIVE");
  Serial.printf("AI/ML Engine: %s\n", g_enhanced_capabilities.ai_ml_engine_active ? "ACTIVE" : "INACTIVE");
  Serial.printf("In-flight Safety: %s\n", g_enhanced_capabilities.inflight_safety_active ? "ACTIVE" : "INACTIVE");
  
  Serial.println("\n--- Performance Metrics ---");
  if (g_enhanced_capabilities.performance_monitoring_active) {
    SystemPerformance_t perf = PerformanceMonitor_GetSystemMetrics();
    Serial.printf("CPU Usage: %.1f%%\n", perf.overall_cpu_usage);
    Serial.printf("Free Heap: %lu bytes\n", perf.free_heap);
    Serial.printf("Performance Warning: %s\n", perf.performance_warning ? "YES" : "NO");
  } else {
    Serial.println("Performance monitoring not active");
  }
  
  Serial.println("\n--- Safety Status ---");
  if (g_enhanced_capabilities.enhanced_safety_active) {
    FailsafeLevel_t level = EnhancedSafety_GetFailsafeLevel();
    Serial.printf("Failsafe Level: %d\n", level);
    Serial.printf("RTH Active: %s\n", EnhancedSafety_IsRTHActive() ? "YES" : "NO");
  } else {
    Serial.println("Enhanced safety not active");
  }
  
  Serial.println("=====================================");
}

// Get last error
QuadFlyEnhancedError_t QuadFlyEnhanced_GetLastError(void) {
  return last_error;
}

// Get error string
const char* QuadFlyEnhanced_GetErrorString(QuadFlyEnhancedError_t error) {
  switch (error) {
    case ENHANCED_ERROR_NONE: return "No error";
    case ENHANCED_ERROR_INITIALIZATION_FAILED: return "Initialization failed";
    case ENHANCED_ERROR_CONFIGURATION_INVALID: return "Invalid configuration";
    case ENHANCED_ERROR_FEATURE_NOT_AVAILABLE: return "Feature not available";
    case ENHANCED_ERROR_SYSTEM_OVERLOAD: return "System overload";
    case ENHANCED_ERROR_SAFETY_VIOLATION: return "Safety violation";
    case ENHANCED_ERROR_MEMORY_EXHAUSTED: return "Memory exhausted";
    case ENHANCED_ERROR_SENSOR_FAILURE: return "Sensor failure";
    case ENHANCED_ERROR_AI_MODEL_FAILURE: return "AI model failure";
    case ENHANCED_ERROR_COMMUNICATION_FAILURE: return "Communication failure";
    default: return "Unknown error";
  }
}

// Register system callback
bool QuadFlyEnhanced_RegisterSystemCallback(SystemEventCallback_t callback) {
  system_event_callback = callback;
  return true;
}

// Register performance callback
bool QuadFlyEnhanced_RegisterPerformanceCallback(PerformanceCallback_t callback) {
  performance_callback = callback;
  return true;
}

// Register safety callback
bool QuadFlyEnhanced_RegisterSafetyCallback(SafetyCallback_t callback) {
  safety_callback = callback;
  return true;
}

// Internal function implementations

static void updateCapabilities(void) {
  // Update performance metrics
  if (g_enhanced_capabilities.performance_monitoring_active) {
    SystemPerformance_t perf = PerformanceMonitor_GetSystemMetrics();
    g_enhanced_capabilities.cpu_usage_percent = perf.overall_cpu_usage;
    g_enhanced_capabilities.free_memory_bytes = perf.free_heap;
  }
  
  // Update safety status
  if (g_enhanced_capabilities.enhanced_safety_active) {
    g_enhanced_capabilities.current_failsafe_level = EnhancedSafety_GetFailsafeLevel();
    g_enhanced_capabilities.emergency_systems_ready = true;
  }
  
  // Update rates (placeholder values)
  g_enhanced_capabilities.sensor_fusion_rate_hz = 200;
  g_enhanced_capabilities.control_loop_rate_hz = 100;
}

static void checkSystemHealth(void) {
  float health = QuadFlyEnhanced_GetSystemHealth();
  
  if (health < 50.0f && system_event_callback != NULL) {
    system_event_callback("System health critical", 3);
  } else if (health < 70.0f && system_event_callback != NULL) {
    system_event_callback("System health warning", 2);
  }
}

static void handleSystemEvent(const char* event, uint8_t severity) {
  Serial.printf("SYSTEM EVENT [%d]: %s\n", severity, event);
}

static void handlePerformanceUpdate(float cpu_usage, uint32_t free_memory) {
  if (performance_callback != NULL) {
    performance_callback(cpu_usage, free_memory);
  }
}

static void handleSafetyEvent(FailsafeTrigger_t trigger, FailsafeLevel_t level) {
  if (safety_callback != NULL) {
    safety_callback(trigger, level);
  }

  Serial.printf("SAFETY EVENT: Trigger %d, Level %d\n", trigger, level);
}

// Update enhanced system (main update function)
void QuadFlyEnhanced_Update(void) {
  if (!enhanced_system_initialized) return;

  uint32_t current_time = millis();

  // Update at 10Hz to avoid overwhelming the system
  if (current_time - last_update_time < 100) return;

  // Update subsystems
  if (g_enhanced_capabilities.performance_monitoring_active) {
    PerformanceMonitor_UpdateTaskMetrics();
  }

  if (g_enhanced_capabilities.enhanced_safety_active) {
    EnhancedSafety_UpdateFailsafeState();
  }

  // Update system capabilities
  updateCapabilities();

  // Check system health
  checkSystemHealth();

  last_update_time = current_time;
}
