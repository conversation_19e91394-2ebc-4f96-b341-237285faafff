/*
 * QuadFly Flight Controller - Safety Systems Implementation
 * 
 * Advanced safety monitoring with multi-level warnings and emergency handling
 */

#include "safety_systems.h"

// Safety system state
static SafetyLevel_t overallSafetyLevel = SAFETY_LEVEL_OK;
static uint32_t lastSafetyCheck = 0;
static bool emergencyLandingActive = false;

// Initialize safety systems
void initializeSafetySystems(void) {
  Serial.println("Initializing safety systems...");
  
  overallSafetyLevel = SAFETY_LEVEL_OK;
  emergencyLandingActive = false;
  
  Serial.println("Safety systems initialized");
}

// Main safety system check
void checkSafetySystems(SystemState_t *state, FlightData_t *flightData) {
  uint32_t currentTime = millis();
  
  // Run safety checks every 100ms
  if (currentTime - lastSafetyCheck >= 100) {
    SafetyLevel_t maxLevel = SAFETY_LEVEL_OK;
    
    // Check all safety systems
    SafetyLevel_t attitudeLevel = checkAttitudeSafety(&flightData->attitude);
    SafetyLevel_t altitudeLevel = checkAltitudeSafety(&flightData->altitude);
    SafetyLevel_t gpsLevel = checkGPSSafety(&flightData->gps);
    SafetyLevel_t batteryLevel = checkBatterySafety(&flightData->battery);
    SafetyLevel_t receiverLevel = checkReceiverSafety(&flightData->receiver);
    
    // Debug output (once every 5 seconds)
    static uint32_t lastDebugTime = 0;
    if (currentTime - lastDebugTime > 5000) {
      Serial.print("Safety Levels - Attitude: ");
      Serial.print(attitudeLevel);
      Serial.print(", Altitude: ");
      Serial.print(altitudeLevel);
      Serial.print(", GPS: ");
      Serial.print(gpsLevel);
      Serial.print(", Battery: ");
      Serial.print(batteryLevel);
      Serial.print(", Receiver: ");
      Serial.println(receiverLevel);
      lastDebugTime = currentTime;
    }
    
    // Determine overall safety level
    maxLevel = max(maxLevel, attitudeLevel);
    maxLevel = max(maxLevel, altitudeLevel);
    maxLevel = max(maxLevel, gpsLevel);
    maxLevel = max(maxLevel, batteryLevel);
    maxLevel = max(maxLevel, receiverLevel);
    
    // Handle safety level changes
    if (maxLevel != overallSafetyLevel) {
      overallSafetyLevel = maxLevel;
      
      switch (overallSafetyLevel) {
        case SAFETY_LEVEL_WARNING:
          Serial.println("SAFETY WARNING: System monitoring detected issues");
          // Flash yellow LED
          break;
          
        case SAFETY_LEVEL_CRITICAL:
          Serial.println("SAFETY CRITICAL: Immediate attention required");
          // Flash red LED
          // Sound warning beeps
          for (int i = 0; i < 5; i++) {
            digitalWrite(BUZZER_PIN, HIGH);
            delay(100);
            digitalWrite(BUZZER_PIN, LOW);
            delay(100);
          }
          break;
          
        case SAFETY_LEVEL_EMERGENCY:
          Serial.println("SAFETY EMERGENCY: Emergency mode detected (disabled for testing)");
          // Temporarily disabled for testing
          // state->emergencyMode = true;
          break;
          
        default:
          break;
      }
    }
    
    lastSafetyCheck = currentTime;
  }
}

// Check failsafe conditions
void checkFailsafeConditions(SystemState_t *state, FlightData_t *flightData) {
  static uint32_t signalLossTime = 0;
  static bool signalLossActive = false;
  
  uint32_t currentTime = millis();
  
  // Check receiver signal loss
  if (flightData->receiver.signalLost) {
    if (!signalLossActive) {
      signalLossActive = true;
      signalLossTime = currentTime;
      Serial.println("FAILSAFE: Receiver signal lost");
    } else {
      // Signal lost for more than 2 seconds
      if (currentTime - signalLossTime > 2000) {
        if (state->armed) {
          Serial.println("FAILSAFE: Activating emergency RTH");
          
          // If GPS available, activate RTH
          if (flightData->gps.fix && flightData->gps.satellites >= 6) {
            state->flightMode = FLIGHT_MODE_RTH;
          } else {
            // No GPS, activate emergency landing
            state->emergencyMode = true;
          }
        }
      }
    }
  } else {
    signalLossActive = false;
  }
  
  // Check critical battery voltage
  if (flightData->battery.criticalVoltageWarning && state->armed) {
    Serial.println("FAILSAFE: Critical battery voltage - Emergency landing");
    state->emergencyMode = true;
  }
  
  // Check extreme attitude angles
  if (state->armed) {
    float maxTiltRad = 60.0f * DEG_TO_RAD; // 60 degrees maximum
    
    if (abs(flightData->attitude.roll) > maxTiltRad || 
        abs(flightData->attitude.pitch) > maxTiltRad) {
      Serial.println("FAILSAFE: Extreme attitude detected");
      state->emergencyMode = true;
    }
  }
}

// Handle emergency mode
void handleEmergencyMode(SystemState_t *state, FlightData_t *flightData) {
  if (!emergencyLandingActive) {
    emergencyLandingActive = true;
    Serial.println("EMERGENCY MODE: Initiating emergency procedures");
    
    // Sound continuous alarm
    digitalWrite(BUZZER_PIN, HIGH);
    delay(1000);
    digitalWrite(BUZZER_PIN, LOW);
  }
  
  // Emergency mode is handled in flight_modes.cpp
  // This function handles additional emergency procedures
  
  // Log emergency event
  Serial.print("EMERGENCY: Time=");
  Serial.print(millis());
  Serial.print(", Alt=");
  Serial.print(flightData->altitude.altitude);
  Serial.print(", Battery=");
  Serial.print(flightData->battery.voltage);
  Serial.print(", GPS=");
  Serial.println(flightData->gps.fix ? "OK" : "LOST");
}

// Update battery monitoring
void updateBatteryMonitoring(BatteryData_t *battery) {
  // Read battery voltage (example values - adjust for your voltage divider)
  int adcValue = analogRead(BATTERY_VOLTAGE_PIN);
  battery->voltage = (adcValue / 4096.0f) * 3.3f * 4.0f; // Assuming 4:1 voltage divider
  
  // Read battery current (if current sensor available)
  int currentADC = analogRead(BATTERY_CURRENT_PIN);
  battery->current = (currentADC / 4096.0f) * 3.3f; // Simplified - adjust for your sensor
  
  // Calculate battery percentage (simplified LiPo curve)
  float cellVoltage = battery->voltage / 3.0f; // Assuming 3S battery
  
  if (cellVoltage >= 4.0f) {
    battery->percentage = 100;
  } else if (cellVoltage >= 3.8f) {
    battery->percentage = 80 + (cellVoltage - 3.8f) / 0.2f * 20;
  } else if (cellVoltage >= 3.6f) {
    battery->percentage = 40 + (cellVoltage - 3.6f) / 0.2f * 40;
  } else if (cellVoltage >= 3.3f) {
    battery->percentage = 10 + (cellVoltage - 3.3f) / 0.3f * 30;
  } else {
    battery->percentage = 0;
  }
  
  // Check voltage thresholds
  battery->lowVoltageWarning = (battery->voltage < DEFAULT_LOW_VOLTAGE / 1000.0f);
  battery->criticalVoltageWarning = (battery->voltage < DEFAULT_CRITICAL_VOLTAGE / 1000.0f);
}

// Check attitude safety
SafetyLevel_t checkAttitudeSafety(AttitudeData_t *attitude) {
  float maxSafeAngle = 45.0f * DEG_TO_RAD; // 45 degrees
  float maxCriticalAngle = 60.0f * DEG_TO_RAD; // 60 degrees
  
  float maxAngle = max(abs(attitude->roll), abs(attitude->pitch));
  
  if (maxAngle > maxCriticalAngle) {
    return SAFETY_LEVEL_EMERGENCY;
  } else if (maxAngle > maxSafeAngle) {
    return SAFETY_LEVEL_CRITICAL;
  } else if (maxAngle > 30.0f * DEG_TO_RAD) {
    return SAFETY_LEVEL_WARNING;
  }
  
  return SAFETY_LEVEL_OK;
}

// Check altitude safety
SafetyLevel_t checkAltitudeSafety(AltitudeData_t *altitude) {
  // Check for excessive altitude
  if (altitude->altitude > 120.0f) { // 120m AGL limit
    return SAFETY_LEVEL_CRITICAL;
  } else if (altitude->altitude > 100.0f) {
    return SAFETY_LEVEL_WARNING;
  }
  
  // Check for ground proximity
  if (altitude->altitude < 0.5f && abs(altitude->verticalSpeed) > 2.0f) {
    return SAFETY_LEVEL_WARNING;
  }
  
  return SAFETY_LEVEL_OK;
}

// Check GPS safety
SafetyLevel_t checkGPSSafety(GPSData_t *gps) {
  uint32_t currentTime = millis();
  
  // Check GPS timeout
  if (currentTime - gps->lastUpdate > 5000) {
    return SAFETY_LEVEL_WARNING;
  }
  
  // Check GPS quality
  if (gps->fix) {
    if (gps->satellites < 4) {
      return SAFETY_LEVEL_WARNING;
    } else if (gps->hdop > 2.0f) {
      return SAFETY_LEVEL_WARNING;
    }
  }
  
  return SAFETY_LEVEL_OK;
}

// Check battery safety
SafetyLevel_t checkBatterySafety(BatteryData_t *battery) {
  if (battery->criticalVoltageWarning) {
    return SAFETY_LEVEL_EMERGENCY;
  } else if (battery->lowVoltageWarning) {
    return SAFETY_LEVEL_CRITICAL;
  } else if (battery->percentage < 20) {
    return SAFETY_LEVEL_WARNING;
  }
  
  return SAFETY_LEVEL_OK;
}

// Check receiver safety
SafetyLevel_t checkReceiverSafety(ReceiverData_t *receiver) {
  uint32_t currentTime = millis();
  
  if (receiver->signalLost) {
    return SAFETY_LEVEL_EMERGENCY;
  } else if (currentTime - receiver->lastUpdate > 1000) {
    return SAFETY_LEVEL_CRITICAL;
  } else if (receiver->signalQuality < 50) {
    return SAFETY_LEVEL_WARNING;
  }
  
  return SAFETY_LEVEL_OK;
}