/*
 * QuadFly Flight Controller - Enhanced Systems Integration Header
 * 
 * Master header for all enhanced features and systems
 */

#ifndef QUADFLY_ENHANCED_H
#define QUADFLY_ENHANCED_H

// Core utilities
#include "math_utils.h"
#include "system_utils.h"

// Hardware Abstraction Layer
#include "../hal/hal.h"
#include "../hal/hal_gpio.h"
#include "../hal/hal_pwm.h"

// Enhanced configuration management
#include "config_manager.h"

// Performance and monitoring
#include "performance_monitor.h"
#include "memory_manager.h"

// Advanced sensor fusion
#include "sensor_fusion.h"

// AI/ML capabilities
#include "ai_ml_engine.h"

// Enhanced safety systems
#include "enhanced_safety.h"
#include "inflight_safety.h"

// Enhanced system version
#define QUADFLY_ENHANCED_VERSION "2.0.0"
#define QUADFLY_ENHANCED_BUILD_DATE __DATE__
#define QUADFLY_ENHANCED_BUILD_TIME __TIME__

// Feature flags
#define FEATURE_ENHANCED_CONFIG     1
#define FEATURE_PERFORMANCE_MONITOR 1
#define FEATURE_MEMORY_MANAGER      1
#define FEATURE_ADVANCED_FUSION     1
#define FEATURE_AI_ML_ENGINE        1
#define FEATURE_ENHANCED_SAFETY     1
#define FEATURE_INFLIGHT_SAFETY     1
#define FEATURE_HAL_ABSTRACTION     1

// System capabilities
typedef struct {
  bool enhanced_config_available;
  bool performance_monitoring_active;
  bool memory_management_active;
  bool advanced_sensor_fusion_active;
  bool ai_ml_engine_active;
  bool enhanced_safety_active;
  bool inflight_safety_active;
  bool hal_abstraction_active;
  
  // Performance metrics
  float cpu_usage_percent;
  uint32_t free_memory_bytes;
  uint32_t sensor_fusion_rate_hz;
  uint32_t control_loop_rate_hz;
  
  // Safety status
  FailsafeLevel_t current_failsafe_level;
  uint8_t active_safety_systems;
  bool emergency_systems_ready;
  
  // AI/ML status
  bool flight_optimization_active;
  bool predictive_maintenance_active;
  float model_confidence;
  
} QuadFlyEnhancedCapabilities_t;

// Enhanced system configuration
typedef struct {
  // Core system settings
  bool enable_enhanced_features;
  uint8_t system_performance_level;  // 0=basic, 1=normal, 2=high, 3=maximum
  
  // Feature enable/disable
  bool enable_performance_monitoring;
  bool enable_memory_management;
  bool enable_advanced_sensor_fusion;
  bool enable_ai_ml_engine;
  bool enable_enhanced_safety;
  bool enable_inflight_safety;
  
  // Performance settings
  PerformanceConfig_t performance_config;
  MemoryManagerConfig_t memory_config;
  AIMLConfig_t aiml_config;
  InFlightSafetyConfig_t inflight_config;
  
  // Safety settings
  SafetyLimits_t safety_limits;
  bool enable_redundant_systems;
  bool enable_predictive_failsafe;
  
} QuadFlyEnhancedConfig_t;

// Function declarations

// System initialization and management
bool QuadFlyEnhanced_Init(void);
void QuadFlyEnhanced_DeInit(void);
bool QuadFlyEnhanced_Configure(const QuadFlyEnhancedConfig_t* config);
void QuadFlyEnhanced_Update(void);
void QuadFlyEnhanced_Reset(void);

// Capability management
QuadFlyEnhancedCapabilities_t QuadFlyEnhanced_GetCapabilities(void);
bool QuadFlyEnhanced_EnableFeature(const char* feature_name, bool enable);
bool QuadFlyEnhanced_IsFeatureEnabled(const char* feature_name);
void QuadFlyEnhanced_OptimizePerformance(void);

// System health and diagnostics
float QuadFlyEnhanced_GetSystemHealth(void);
bool QuadFlyEnhanced_RunSystemDiagnostics(void);
void QuadFlyEnhanced_PrintSystemStatus(void);
void QuadFlyEnhanced_LogSystemMetrics(void);

// Enhanced flight control integration
void QuadFlyEnhanced_UpdateFlightControl(void);
bool QuadFlyEnhanced_OptimizeFlightParameters(void);
void QuadFlyEnhanced_AdaptToConditions(void);

// Safety system integration
void QuadFlyEnhanced_UpdateSafetySystems(void);
bool QuadFlyEnhanced_CheckSafetyStatus(void);
void QuadFlyEnhanced_HandleSafetyEvent(FailsafeTrigger_t trigger);

// Configuration management
bool QuadFlyEnhanced_SaveConfiguration(void);
bool QuadFlyEnhanced_LoadConfiguration(void);
void QuadFlyEnhanced_ResetToDefaults(void);
bool QuadFlyEnhanced_BackupConfiguration(const char* backup_name);
bool QuadFlyEnhanced_RestoreConfiguration(const char* backup_name);

// Performance optimization
void QuadFlyEnhanced_OptimizeTaskPriorities(void);
void QuadFlyEnhanced_OptimizeMemoryUsage(void);
void QuadFlyEnhanced_OptimizeSensorFusion(void);
void QuadFlyEnhanced_OptimizeControlLoops(void);

// AI/ML integration
bool QuadFlyEnhanced_TrainFlightModel(void);
bool QuadFlyEnhanced_UpdatePredictiveModel(void);
void QuadFlyEnhanced_ApplyMLOptimizations(void);
float QuadFlyEnhanced_GetMLConfidence(void);

// Data logging and analysis
void QuadFlyEnhanced_StartDataLogging(void);
void QuadFlyEnhanced_StopDataLogging(void);
bool QuadFlyEnhanced_AnalyzeFlightData(void);
void QuadFlyEnhanced_GenerateFlightReport(void);

// Remote monitoring and control
bool QuadFlyEnhanced_EnableRemoteMonitoring(bool enable);
void QuadFlyEnhanced_SendTelemetryData(void);
bool QuadFlyEnhanced_HandleRemoteCommand(const char* command, const char* params);

// Maintenance and updates
void QuadFlyEnhanced_ScheduleMaintenance(void);
bool QuadFlyEnhanced_CheckForUpdates(void);
void QuadFlyEnhanced_UpdateFirmware(void);
uint32_t QuadFlyEnhanced_GetMaintenanceInterval(void);

// Error handling and recovery
typedef enum {
  ENHANCED_ERROR_NONE = 0,
  ENHANCED_ERROR_INITIALIZATION_FAILED,
  ENHANCED_ERROR_CONFIGURATION_INVALID,
  ENHANCED_ERROR_FEATURE_NOT_AVAILABLE,
  ENHANCED_ERROR_SYSTEM_OVERLOAD,
  ENHANCED_ERROR_SAFETY_VIOLATION,
  ENHANCED_ERROR_MEMORY_EXHAUSTED,
  ENHANCED_ERROR_SENSOR_FAILURE,
  ENHANCED_ERROR_AI_MODEL_FAILURE,
  ENHANCED_ERROR_COMMUNICATION_FAILURE,
  ENHANCED_ERROR_UNKNOWN
} QuadFlyEnhancedError_t;

QuadFlyEnhancedError_t QuadFlyEnhanced_GetLastError(void);
const char* QuadFlyEnhanced_GetErrorString(QuadFlyEnhancedError_t error);
bool QuadFlyEnhanced_RecoverFromError(QuadFlyEnhancedError_t error);

// System information
void QuadFlyEnhanced_PrintVersionInfo(void);
void QuadFlyEnhanced_PrintFeatureList(void);
void QuadFlyEnhanced_PrintSystemInfo(void);
uint32_t QuadFlyEnhanced_GetUptime(void);

// Callback types for system events
typedef void (*SystemEventCallback_t)(const char* event, uint8_t severity);
typedef void (*PerformanceCallback_t)(float cpu_usage, uint32_t free_memory);
typedef void (*SafetyCallback_t)(FailsafeTrigger_t trigger, FailsafeLevel_t level);

bool QuadFlyEnhanced_RegisterSystemCallback(SystemEventCallback_t callback);
bool QuadFlyEnhanced_RegisterPerformanceCallback(PerformanceCallback_t callback);
bool QuadFlyEnhanced_RegisterSafetyCallback(SafetyCallback_t callback);

// Advanced features
void QuadFlyEnhanced_EnableAdaptiveMode(bool enable);
void QuadFlyEnhanced_EnableLearningMode(bool enable);
void QuadFlyEnhanced_EnablePredictiveMode(bool enable);
bool QuadFlyEnhanced_IsAdaptiveModeActive(void);

// Integration with existing QuadFly systems
void QuadFlyEnhanced_IntegrateWithSensors(void);
void QuadFlyEnhanced_IntegrateWithMotors(void);
void QuadFlyEnhanced_IntegrateWithReceiver(void);
void QuadFlyEnhanced_IntegrateWithGPS(void);
void QuadFlyEnhanced_IntegrateWithTelemetry(void);

// Utility macros for enhanced features
#define ENHANCED_LOG(level, message, ...) \
  do { \
    if (QuadFlyEnhanced_IsFeatureEnabled("logging")) { \
      Serial.printf("[%s] " message "\n", level, ##__VA_ARGS__); \
    } \
  } while(0)

#define ENHANCED_ASSERT(condition, message) \
  do { \
    if (!(condition)) { \
      ENHANCED_LOG("ERROR", "ASSERTION FAILED: %s - %s", #condition, message); \
      QuadFlyEnhanced_HandleSafetyEvent(FAILSAFE_TRIGGER_MANUAL); \
    } \
  } while(0)

#define ENHANCED_PROFILE_START(name) \
  uint32_t _profile_start_##name = esp_timer_get_time()

#define ENHANCED_PROFILE_END(name) \
  do { \
    uint32_t _profile_duration = esp_timer_get_time() - _profile_start_##name; \
    PerformanceMonitor_RecordExecution(xTaskGetCurrentTaskHandle(), _profile_duration); \
  } while(0)

// Global enhanced system instance
extern QuadFlyEnhancedCapabilities_t g_enhanced_capabilities;
extern QuadFlyEnhancedConfig_t g_enhanced_config;

#endif // QUADFLY_ENHANCED_H
