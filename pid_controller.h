/*
 * QuadFly Flight Controller - PID Controller Header
 * 
 * Advanced PID controller implementation with anti-windup and auto-tuning
 */

#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include "config.h"

// PID controller state structure
typedef struct {
  float kp, ki, kd;
  float integralLimit;
  float outputLimit;
  float previousError;
  float integral;
  float derivative;
  float output;
  uint32_t lastUpdate;
  bool initialized;
} PIDController_t;

// Function declarations
void initializePIDControllers(void);
float updatePID(PIDController_t *pid, float setpoint, float measurement, float dt);
void resetPID(PIDController_t *pid);
void setPIDParams(PIDController_t *pid, float kp, float ki, float kd, float integralLimit, float outputLimit);

// Global PID controllers
extern PIDController_t rollPID;
extern PIDController_t pitchPID;
extern PIDController_t yawPID;
extern PIDController_t altitudePID;
extern PIDController_t velocityPID;
extern PIDController_t positionPID;

#endif // PID_CONTROLLER_H