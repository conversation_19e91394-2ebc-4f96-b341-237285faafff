/*
 * QuadFly Flight Controller - Enhanced Safety Systems Implementation
 * 
 * Multi-stage failsafes, geofencing, RTH, and redundant sensor voting
 */

#include "enhanced_safety.h"
#include "system_utils.h"
#include <math.h>

// Global safety state
static bool safety_initialized = false;
static FailsafeState_t failsafe_state = {0};
static SafetyLimits_t safety_limits = {0};
static RTHConfig_t rth_config = {0};
static BatteryFailsafeConfig_t battery_config = {0};

// Geofencing
static Geofence_t geofences[8];
static uint8_t geofence_count = 0;

// Sensor voting systems
static SensorVoting_t sensor_voting[SENSOR_TYPE_COUNT];

// Safety callbacks
static SafetyEventCallback_t safety_event_callback = NULL;
static GeofenceBreachCallback_t geofence_callback = NULL;
static RTHCallback_t rth_callback = NULL;

// Safety event logging
static uint32_t safety_event_counts[FAILSAFE_TRIGGER_COUNT] = {0};
static uint32_t last_safety_update = 0;

// RTH state
static bool rth_active = false;
static uint32_t rth_start_time = 0;
static Vector3_t rth_waypoints[10];
static uint8_t rth_waypoint_count = 0;
static uint8_t current_rth_waypoint = 0;

// Initialize enhanced safety systems
bool EnhancedSafety_Init(void) {
  if (safety_initialized) {
    return true;
  }
  
  // Initialize failsafe state
  memset(&failsafe_state, 0, sizeof(FailsafeState_t));
  failsafe_state.current_level = FAILSAFE_LEVEL_NONE;
  failsafe_state.previous_level = FAILSAFE_LEVEL_NONE;
  failsafe_state.system_armed = false;
  
  // Initialize default safety limits
  safety_limits.max_altitude_m = 120.0f;  // 400 feet
  safety_limits.max_distance_m = 500.0f;
  safety_limits.max_speed_ms = 15.0f;
  safety_limits.max_vertical_speed_ms = 5.0f;
  safety_limits.max_tilt_angle_deg = 45.0f;
  safety_limits.max_yaw_rate_dps = 180.0f;
  safety_limits.min_battery_voltage_mv = 3300;
  safety_limits.max_temperature_c = 85.0f;
  safety_limits.max_vibration_g = 5.0f;
  safety_limits.max_flight_time_s = 1800; // 30 minutes
  
  // Initialize RTH configuration
  rth_config.return_altitude = 50.0f;
  rth_config.return_speed = 5.0f;
  rth_config.landing_speed = 1.0f;
  rth_config.hover_time_before_land = 5.0f;
  rth_config.auto_land_enabled = true;
  rth_config.precision_landing = false;
  rth_config.timeout_ms = 300000; // 5 minutes
  rth_config.enabled = true;
  
  // Initialize battery failsafe configuration
  battery_config.warning_voltage_mv = 3500;
  battery_config.critical_voltage_mv = 3300;
  battery_config.emergency_voltage_mv = 3000;
  battery_config.warning_capacity_percent = 30;
  battery_config.critical_capacity_percent = 20;
  battery_config.emergency_capacity_percent = 10;
  battery_config.warning_time_remaining_s = 300; // 5 minutes
  battery_config.critical_time_remaining_s = 120; // 2 minutes
  battery_config.enable_smart_rth = true;
  battery_config.enable_voltage_compensation = true;
  
  // Initialize geofences
  memset(geofences, 0, sizeof(geofences));
  geofence_count = 0;
  
  // Initialize sensor voting
  for (uint8_t i = 0; i < SENSOR_TYPE_COUNT; i++) {
    memset(&sensor_voting[i], 0, sizeof(SensorVoting_t));
    sensor_voting[i].agreement_threshold = 0.1f; // 10% agreement threshold
  }
  
  safety_initialized = true;
  Serial.println("Enhanced safety systems initialized successfully");
  return true;
}

// Configure safety limits
bool EnhancedSafety_Configure(const SafetyLimits_t* limits) {
  if (!safety_initialized || limits == NULL) {
    return false;
  }
  
  memcpy(&safety_limits, limits, sizeof(SafetyLimits_t));
  Serial.println("Safety limits configured");
  return true;
}

// Update failsafe state
void EnhancedSafety_UpdateFailsafeState(void) {
  if (!safety_initialized) return;
  
  uint32_t current_time = millis();
  
  // Update trigger timestamps
  for (uint8_t i = 0; i < FAILSAFE_TRIGGER_COUNT; i++) {
    if (failsafe_state.active_triggers & (1 << i)) {
      if (failsafe_state.trigger_timestamps[i] == 0) {
        failsafe_state.trigger_timestamps[i] = current_time;
      }
    }
  }
  
  // Determine current failsafe level based on active triggers
  FailsafeLevel_t new_level = FAILSAFE_LEVEL_NONE;
  
  if (failsafe_state.active_triggers & (1 << FAILSAFE_TRIGGER_MOTOR_FAILURE)) {
    new_level = FAILSAFE_LEVEL_CATASTROPHIC;
  } else if (failsafe_state.active_triggers & (1 << FAILSAFE_TRIGGER_CRITICAL_BATTERY)) {
    new_level = FAILSAFE_LEVEL_EMERGENCY;
  } else if (failsafe_state.active_triggers & (1 << FAILSAFE_TRIGGER_SENSOR_FAILURE)) {
    new_level = FAILSAFE_LEVEL_CRITICAL;
  } else if (failsafe_state.active_triggers & (1 << FAILSAFE_TRIGGER_LOW_BATTERY)) {
    new_level = FAILSAFE_LEVEL_CAUTION;
  } else if (failsafe_state.active_triggers & (1 << FAILSAFE_TRIGGER_RC_LOSS)) {
    new_level = FAILSAFE_LEVEL_WARNING;
  }
  
  // Update failsafe level
  if (new_level != failsafe_state.current_level) {
    failsafe_state.previous_level = failsafe_state.current_level;
    failsafe_state.current_level = new_level;
    failsafe_state.level_change_time = current_time;
    
    // Execute appropriate failsafe actions
    switch (new_level) {
      case FAILSAFE_LEVEL_WARNING:
        EnhancedSafety_ExecuteFailsafeAction(FAILSAFE_ACTION_WARNING_BEEP);
        break;
      case FAILSAFE_LEVEL_CAUTION:
        EnhancedSafety_ExecuteFailsafeAction(FAILSAFE_ACTION_LED_FLASH);
        break;
      case FAILSAFE_LEVEL_CRITICAL:
        EnhancedSafety_ExecuteFailsafeAction(FAILSAFE_ACTION_STABILIZE);
        break;
      case FAILSAFE_LEVEL_EMERGENCY:
        EnhancedSafety_ExecuteFailsafeAction(FAILSAFE_ACTION_RETURN_TO_HOME);
        break;
      case FAILSAFE_LEVEL_CATASTROPHIC:
        EnhancedSafety_ExecuteFailsafeAction(FAILSAFE_ACTION_EMERGENCY_LAND);
        break;
      default:
        break;
    }
    
    // Notify callback
    if (safety_event_callback != NULL) {
      safety_event_callback(FAILSAFE_TRIGGER_MANUAL, new_level);
    }
  }
  
  last_safety_update = current_time;
}

// Trigger failsafe
bool EnhancedSafety_TriggerFailsafe(FailsafeTrigger_t trigger) {
  if (!safety_initialized || trigger >= FAILSAFE_TRIGGER_COUNT) {
    return false;
  }
  
  failsafe_state.active_triggers |= (1 << trigger);
  safety_event_counts[trigger]++;
  
  Serial.printf("Failsafe triggered: %d\n", trigger);
  
  EnhancedSafety_UpdateFailsafeState();
  return true;
}

// Clear failsafe
bool EnhancedSafety_ClearFailsafe(FailsafeTrigger_t trigger) {
  if (!safety_initialized || trigger >= FAILSAFE_TRIGGER_COUNT) {
    return false;
  }
  
  failsafe_state.active_triggers &= ~(1 << trigger);
  failsafe_state.trigger_timestamps[trigger] = 0;
  
  Serial.printf("Failsafe cleared: %d\n", trigger);
  
  EnhancedSafety_UpdateFailsafeState();
  return true;
}

// Get current failsafe level
FailsafeLevel_t EnhancedSafety_GetFailsafeLevel(void) {
  return failsafe_state.current_level;
}

// Check if specific failsafe is active
bool EnhancedSafety_IsFailsafeActive(FailsafeTrigger_t trigger) {
  if (trigger >= FAILSAFE_TRIGGER_COUNT) return false;
  return (failsafe_state.active_triggers & (1 << trigger)) != 0;
}

// Execute failsafe action
void EnhancedSafety_ExecuteFailsafeAction(FailsafeAction_t action) {
  if (!safety_initialized) return;
  
  switch (action) {
    case FAILSAFE_ACTION_WARNING_BEEP:
      // Activate buzzer with warning pattern
      Serial.println("SAFETY: Warning beep activated");
      break;
      
    case FAILSAFE_ACTION_LED_FLASH:
      // Flash status LEDs
      Serial.println("SAFETY: LED flash activated");
      break;
      
    case FAILSAFE_ACTION_REDUCE_POWER:
      // Reduce motor power to 50%
      Serial.println("SAFETY: Power reduced to 50%");
      break;
      
    case FAILSAFE_ACTION_STABILIZE:
      // Switch to stabilize mode
      Serial.println("SAFETY: Switched to stabilize mode");
      break;
      
    case FAILSAFE_ACTION_ALTITUDE_HOLD:
      // Switch to altitude hold mode
      Serial.println("SAFETY: Switched to altitude hold mode");
      break;
      
    case FAILSAFE_ACTION_RETURN_TO_HOME:
      // Start return to home
      if (rth_config.enabled) {
        EnhancedSafety_StartRTH();
      }
      break;
      
    case FAILSAFE_ACTION_EMERGENCY_LAND:
      // Start emergency landing
      Serial.println("SAFETY: Emergency landing initiated");
      break;
      
    case FAILSAFE_ACTION_MOTOR_CUTOFF:
      // Cut motor power (dangerous - only for catastrophic failures)
      Serial.println("SAFETY: MOTOR CUTOFF - EMERGENCY!");
      break;
      
    default:
      break;
  }
}

// Create geofence
bool EnhancedSafety_CreateGeofence(uint8_t fence_id, const Geofence_t* fence) {
  if (!safety_initialized || fence == NULL || fence_id >= 8) {
    return false;
  }
  
  memcpy(&geofences[fence_id], fence, sizeof(Geofence_t));
  
  if (fence_id >= geofence_count) {
    geofence_count = fence_id + 1;
  }
  
  Serial.printf("Geofence %d created (type: %d)\n", fence_id, fence->type);
  return true;
}

// Check geofence violation
bool EnhancedSafety_CheckGeofence(uint8_t fence_id, Vector3_t position) {
  if (!safety_initialized || fence_id >= geofence_count) {
    return false;
  }
  
  Geofence_t* fence = &geofences[fence_id];
  if (!fence->enabled) return false;
  
  bool inside = EnhancedSafety_IsInsideGeofence(fence_id, position);
  
  if (!inside && !fence->breach_detected) {
    // New breach detected
    fence->breach_detected = true;
    fence->breach_time = millis();
    
    // Trigger geofence failsafe
    EnhancedSafety_TriggerFailsafe(FAILSAFE_TRIGGER_GEOFENCE_BREACH);
    
    // Execute breach action
    EnhancedSafety_ExecuteFailsafeAction(fence->breach_action);
    
    // Notify callback
    if (geofence_callback != NULL) {
      geofence_callback(fence_id, position);
    }
    
    Serial.printf("Geofence %d breach detected!\n", fence_id);
  } else if (inside && fence->breach_detected) {
    // Back inside geofence
    fence->breach_detected = false;
    EnhancedSafety_ClearFailsafe(FAILSAFE_TRIGGER_GEOFENCE_BREACH);
    Serial.printf("Geofence %d breach cleared\n", fence_id);
  }
  
  return !inside; // Return true if violation detected
}

// Check if position is inside geofence
bool EnhancedSafety_IsInsideGeofence(uint8_t fence_id, Vector3_t position) {
  if (!safety_initialized || fence_id >= geofence_count) {
    return true; // Assume safe if fence doesn't exist
  }
  
  Geofence_t* fence = &geofences[fence_id];
  if (!fence->enabled) return true;
  
  switch (fence->type) {
    case GEOFENCE_TYPE_CYLINDER: {
      // Check horizontal distance from center
      float dx = position.x - fence->center.x;
      float dy = position.y - fence->center.y;
      float horizontal_distance = sqrt(dx*dx + dy*dy);
      
      // Check altitude limits
      bool altitude_ok = (position.z >= fence->height_min && position.z <= fence->height_max);
      
      return (horizontal_distance <= fence->radius) && altitude_ok;
    }
    
    case GEOFENCE_TYPE_SPHERE: {
      // Check 3D distance from center
      float dx = position.x - fence->center.x;
      float dy = position.y - fence->center.y;
      float dz = position.z - fence->center.z;
      float distance = sqrt(dx*dx + dy*dy + dz*dz);
      
      return distance <= fence->radius;
    }
    
    case GEOFENCE_TYPE_BOX: {
      // Simple box check (assuming vertices define min/max bounds)
      if (fence->vertex_count >= 2) {
        Vector3_t min_bounds = fence->vertices[0];
        Vector3_t max_bounds = fence->vertices[1];
        
        return (position.x >= min_bounds.x && position.x <= max_bounds.x &&
                position.y >= min_bounds.y && position.y <= max_bounds.y &&
                position.z >= min_bounds.z && position.z <= max_bounds.z);
      }
      return true;
    }
    
    default:
      return true; // Unknown fence type - assume safe
  }
}

// Configure RTH
bool EnhancedSafety_ConfigureRTH(const RTHConfig_t* config) {
  if (!safety_initialized || config == NULL) {
    return false;
  }
  
  memcpy(&rth_config, config, sizeof(RTHConfig_t));
  Serial.println("RTH configuration updated");
  return true;
}

// Set home position
bool EnhancedSafety_SetHomePosition(Vector3_t position) {
  if (!safety_initialized) return false;
  
  rth_config.home_position = position;
  Serial.printf("Home position set: (%.2f, %.2f, %.2f)\n", 
                position.x, position.y, position.z);
  return true;
}

// Start RTH
bool EnhancedSafety_StartRTH(void) {
  if (!safety_initialized || !rth_config.enabled || rth_active) {
    return false;
  }
  
  rth_active = true;
  rth_start_time = millis();
  current_rth_waypoint = 0;
  
  // Simple RTH: go to return altitude, then home, then land
  rth_waypoint_count = 2;
  rth_waypoints[0] = {rth_config.home_position.x, rth_config.home_position.y, rth_config.return_altitude};
  rth_waypoints[1] = rth_config.home_position;
  
  Serial.println("Return to Home initiated");
  
  if (rth_callback != NULL) {
    rth_callback(true, 0.0f);
  }
  
  return true;
}

// Check if RTH is active
bool EnhancedSafety_IsRTHActive(void) {
  return rth_active;
}

// Get RTH progress
float EnhancedSafety_GetRTHProgress(void) {
  if (!rth_active || rth_waypoint_count == 0) return 0.0f;
  
  return (float)current_rth_waypoint / (float)rth_waypoint_count;
}

// Initialize sensor voting
bool EnhancedSafety_InitSensorVoting(uint8_t sensor_type, uint8_t sensor_count) {
  if (!safety_initialized || sensor_type >= SENSOR_TYPE_COUNT || sensor_count > 4) {
    return false;
  }
  
  SensorVoting_t* voting = &sensor_voting[sensor_type];
  voting->sensor_count = sensor_count;
  voting->voting_active = true;
  
  // Initialize all sensors as valid with equal weight
  for (uint8_t i = 0; i < sensor_count; i++) {
    voting->sensor_valid[i] = true;
    voting->sensor_weights[i] = 1.0f / sensor_count;
  }
  
  Serial.printf("Sensor voting initialized for type %d with %d sensors\n", 
                sensor_type, sensor_count);
  return true;
}

// Update sensor vote
void EnhancedSafety_UpdateSensorVote(uint8_t sensor_type, uint8_t sensor_id, float value, bool valid) {
  if (!safety_initialized || sensor_type >= SENSOR_TYPE_COUNT || sensor_id >= 4) {
    return;
  }
  
  SensorVoting_t* voting = &sensor_voting[sensor_type];
  if (!voting->voting_active || sensor_id >= voting->sensor_count) {
    return;
  }
  
  voting->sensor_values[sensor_id] = value;
  voting->sensor_valid[sensor_id] = valid;
  voting->sensor_timestamps[sensor_id] = millis();
  
  // Calculate voted value using weighted average of valid sensors
  float total_weight = 0.0f;
  float weighted_sum = 0.0f;
  uint8_t valid_sensors = 0;
  
  for (uint8_t i = 0; i < voting->sensor_count; i++) {
    if (voting->sensor_valid[i]) {
      weighted_sum += voting->sensor_values[i] * voting->sensor_weights[i];
      total_weight += voting->sensor_weights[i];
      valid_sensors++;
    }
  }
  
  if (total_weight > 0.0f) {
    voting->voted_value = weighted_sum / total_weight;
    voting->confidence = (float)valid_sensors / (float)voting->sensor_count;
  } else {
    voting->confidence = 0.0f;
  }
  
  voting->failed_sensors = voting->sensor_count - valid_sensors;
}

// Get voted sensor value
float EnhancedSafety_GetVotedSensorValue(uint8_t sensor_type) {
  if (!safety_initialized || sensor_type >= SENSOR_TYPE_COUNT) {
    return 0.0f;
  }
  
  return sensor_voting[sensor_type].voted_value;
}

// Get sensor confidence
float EnhancedSafety_GetSensorConfidence(uint8_t sensor_type) {
  if (!safety_initialized || sensor_type >= SENSOR_TYPE_COUNT) {
    return 0.0f;
  }
  
  return sensor_voting[sensor_type].confidence;
}

// Print safety status
void EnhancedSafety_PrintSafetyStatus(void) {
  if (!safety_initialized) return;
  
  Serial.println("=== Enhanced Safety Status ===");
  Serial.printf("Failsafe Level: %d\n", failsafe_state.current_level);
  Serial.printf("Active Triggers: 0x%08lX\n", failsafe_state.active_triggers);
  Serial.printf("System Armed: %s\n", failsafe_state.system_armed ? "YES" : "NO");
  Serial.printf("RTH Active: %s\n", rth_active ? "YES" : "NO");
  
  if (rth_active) {
    Serial.printf("RTH Progress: %.1f%%\n", EnhancedSafety_GetRTHProgress() * 100.0f);
  }
  
  Serial.printf("Geofences: %d configured\n", geofence_count);
  
  for (uint8_t i = 0; i < SENSOR_TYPE_COUNT; i++) {
    if (sensor_voting[i].voting_active) {
      Serial.printf("Sensor Type %d: %.2f (confidence: %.2f)\n", 
                    i, sensor_voting[i].voted_value, sensor_voting[i].confidence);
    }
  }
  
  Serial.println("=============================");
}
