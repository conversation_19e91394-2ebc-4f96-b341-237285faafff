/*
 * QuadFly Flight Controller - Enhanced Configuration Manager Header
 * 
 * Advanced configuration management with versioning, validation, and backup
 */

#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include "../config.h"

// Configuration version management
#define CONFIG_MANAGER_VERSION 2
#define CONFIG_SIGNATURE 0x51464C59  // "QFLY" in hex
#define MAX_CONFIG_BACKUPS 3
#define CONFIG_VALIDATION_MAGIC 0xDEADBEEF

// Configuration validation results
typedef enum {
  CONFIG_VALID = 0,
  CONFIG_INVALID_SIGNATURE,
  CONFIG_INVALID_VERSION,
  CONFIG_INVALID_CHECKSUM,
  CONFIG_INVALID_PARAMETERS,
  CONFIG_CORRUPTED,
  CONFIG_NOT_FOUND
} ConfigValidationResult_t;

// Configuration backup status
typedef enum {
  BACKUP_SUCCESS = 0,
  BACKUP_FAILED,
  BACKUP_FULL,
  BACKUP_NOT_FOUND,
  BACKUP_CORRUPTED
} ConfigBackupStatus_t;

// Configuration change tracking
typedef struct {
  uint32_t timestamp;
  uint16_t parameter_id;
  float old_value;
  float new_value;
  char description[32];
} ConfigChange_t;

// Enhanced configuration header
typedef struct {
  uint32_t signature;           // Configuration signature
  uint16_t version;            // Configuration version
  uint16_t size;               // Configuration data size
  uint32_t timestamp;          // Last modification timestamp
  uint32_t checksum;           // CRC32 checksum
  uint32_t validation_magic;   // Validation magic number
  uint8_t backup_count;        // Number of available backups
  uint8_t reserved[7];         // Reserved for future use
} ConfigHeader_t;

// Enhanced configuration structure
typedef struct {
  ConfigHeader_t header;
  
  // Flight control parameters
  PIDParams_t rollPID;
  PIDParams_t pitchPID;
  PIDParams_t yawPID;
  PIDParams_t altPID;
  PIDParams_t velPID;
  PIDParams_t posPID;
  
  // Receiver configuration
  uint16_t rcMin[8];
  uint16_t rcMax[8];
  uint16_t rcMid[8];
  uint8_t rcDeadband;
  uint16_t rcFailsafeValues[8];
  
  // Safety parameters
  float maxTiltAngle;
  float maxVerticalSpeed;
  float maxHorizontalSpeed;
  uint16_t failsafeThrottle;
  uint16_t lowVoltageThreshold;
  uint16_t criticalVoltageThreshold;
  uint16_t geofenceRadius;
  float geofenceHeight;
  
  // Flight parameters
  uint8_t motorIdleSpeed;
  uint8_t motorMaxSpeed;
  bool enableGPS;
  bool enableBarometer;
  bool enableMagnetometer;
  bool enableTelemetry;
  bool enableDataLogging;
  
  // Sensor calibration data
  float accelOffset[3];
  float accelScale[3];
  float gyroOffset[3];
  float magOffset[3];
  float magScale[3];
  float baroOffset;
  
  // Advanced features
  bool enableAI;
  bool enablePredictiveMaintenance;
  uint8_t sensorFusionMode;
  uint8_t flightModeDefault;
  
  // System settings
  uint8_t logLevel;
  uint16_t telemetryRate;
  uint16_t sensorRate;
  uint16_t controlRate;
  
  // User-defined parameters
  float userParams[16];
  char userParamNames[16][16];
  
} EnhancedConfig_t;

// Configuration parameter metadata
typedef struct {
  uint16_t id;
  const char* name;
  const char* description;
  float min_value;
  float max_value;
  float default_value;
  uint8_t data_type;  // 0=float, 1=int, 2=bool
  bool requires_restart;
} ConfigParameter_t;

// Function declarations

// Core configuration management
bool ConfigManager_Init(void);
bool ConfigManager_Load(void);
bool ConfigManager_Save(void);
bool ConfigManager_Reset(void);
ConfigValidationResult_t ConfigManager_Validate(const EnhancedConfig_t* config);

// Configuration versioning
bool ConfigManager_Migrate(uint16_t from_version, uint16_t to_version);
bool ConfigManager_IsVersionSupported(uint16_t version);
uint16_t ConfigManager_GetCurrentVersion(void);

// Backup and restore
ConfigBackupStatus_t ConfigManager_CreateBackup(const char* name);
ConfigBackupStatus_t ConfigManager_RestoreBackup(const char* name);
ConfigBackupStatus_t ConfigManager_ListBackups(char backups[][32], uint8_t max_count);
ConfigBackupStatus_t ConfigManager_DeleteBackup(const char* name);
bool ConfigManager_AutoBackup(void);

// Parameter access
bool ConfigManager_GetParameter(uint16_t param_id, void* value);
bool ConfigManager_SetParameter(uint16_t param_id, const void* value);
bool ConfigManager_GetParameterByName(const char* name, void* value);
bool ConfigManager_SetParameterByName(const char* name, const void* value);
const ConfigParameter_t* ConfigManager_GetParameterInfo(uint16_t param_id);

// Validation and integrity
bool ConfigManager_ValidateParameter(uint16_t param_id, const void* value);
bool ConfigManager_ValidateAllParameters(void);
uint32_t ConfigManager_CalculateChecksum(const EnhancedConfig_t* config);
bool ConfigManager_VerifyIntegrity(void);

// Change tracking
bool ConfigManager_LogChange(uint16_t param_id, float old_value, float new_value);
uint8_t ConfigManager_GetChangeHistory(ConfigChange_t* changes, uint8_t max_count);
void ConfigManager_ClearChangeHistory(void);

// Remote configuration
bool ConfigManager_HandleRemoteCommand(const char* command, const char* params);
bool ConfigManager_SendConfiguration(void);
bool ConfigManager_ReceiveConfiguration(const uint8_t* data, uint16_t length);

// Import/Export
bool ConfigManager_ExportToJSON(char* json_buffer, uint16_t buffer_size);
bool ConfigManager_ImportFromJSON(const char* json_data);
bool ConfigManager_ExportToBinary(uint8_t* buffer, uint16_t* size);
bool ConfigManager_ImportFromBinary(const uint8_t* buffer, uint16_t size);

// Utility functions
void ConfigManager_PrintConfiguration(void);
void ConfigManager_PrintParameterInfo(void);
bool ConfigManager_IsParameterChanged(uint16_t param_id);
void ConfigManager_MarkParameterClean(uint16_t param_id);
uint32_t ConfigManager_GetLastModified(void);

// Configuration profiles
bool ConfigManager_SaveProfile(const char* profile_name);
bool ConfigManager_LoadProfile(const char* profile_name);
bool ConfigManager_DeleteProfile(const char* profile_name);
uint8_t ConfigManager_ListProfiles(char profiles[][32], uint8_t max_count);

// Factory reset and defaults
bool ConfigManager_FactoryReset(void);
bool ConfigManager_LoadDefaults(void);
bool ConfigManager_IsFactoryDefault(void);

// Configuration locking
bool ConfigManager_Lock(void);
bool ConfigManager_Unlock(const char* password);
bool ConfigManager_IsLocked(void);
bool ConfigManager_SetPassword(const char* password);

// Error handling
typedef enum {
  CONFIG_ERROR_NONE = 0,
  CONFIG_ERROR_INVALID_PARAM,
  CONFIG_ERROR_OUT_OF_RANGE,
  CONFIG_ERROR_READ_ONLY,
  CONFIG_ERROR_STORAGE_FULL,
  CONFIG_ERROR_CORRUPTED,
  CONFIG_ERROR_LOCKED,
  CONFIG_ERROR_UNKNOWN
} ConfigError_t;

ConfigError_t ConfigManager_GetLastError(void);
const char* ConfigManager_GetErrorString(ConfigError_t error);

// Global configuration access
extern EnhancedConfig_t g_config;

#endif // CONFIG_MANAGER_H
