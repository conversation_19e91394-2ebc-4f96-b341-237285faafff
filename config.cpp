/*
 * QuadFly Flight Controller - Configuration Management
 * 
 * Handles loading and saving of flight controller parameters
 */
#include "config.h"
#include <EEPROM.h>

// Configuration structure
typedef struct {
  float pidP[3];        // PID P gains [Roll, Pitch, Yaw]
  float pidI[3];        // PID I gains
  float pidD[3];        // PID D gains
  float gyroCalibration[3];  // Gyro calibration offsets
  float accCalibration[3];   // Accelerometer calibration offsets
  uint16_t configVersion;    // Configuration version
  uint32_t checksum;         // Data integrity checksum
} Config_t;

// Default configuration values
Config_t defaultConfig = {
  .pidP = {1.0, 1.0, 1.0},
  .pidI = {0.1, 0.1, 0.1},
  .pidD = {0.05, 0.05, 0.05},
  .gyroCalibration = {0.0, 0.0, 0.0},
  .accCalibration = {0.0, 0.0, 0.0},
  .configVersion = CONFIG_VERSION,
  .checksum = 0
};

Config_t currentConfig;

// Calculate checksum for configuration data
uint32_t calculateChecksum(Config_t* config) {
  uint32_t checksum = 0;
  uint8_t* data = (uint8_t*)config;
  
  for (int i = 0; i < sizeof(Config_t) - sizeof(uint32_t); i++) {
    checksum += data[i];
  }
  
  return checksum;
}

// Load configuration from EEPROM
bool loadConfiguration(void) {
  Serial.println("Loading configuration from EEPROM...");
  
  // Initialize EEPROM
  EEPROM.begin(sizeof(Config_t));
  
  // Read configuration from EEPROM
  EEPROM.get(0, currentConfig);
  
  // Verify configuration version and checksum
  if (currentConfig.configVersion != CONFIG_VERSION) {
    Serial.println("Configuration version mismatch, loading defaults");
    currentConfig = defaultConfig;
    saveConfiguration();
    return false;
  }
  
  uint32_t calculatedChecksum = calculateChecksum(&currentConfig);
  if (calculatedChecksum != currentConfig.checksum) {
    Serial.println("Configuration checksum mismatch, loading defaults");
    currentConfig = defaultConfig;
    saveConfiguration();
    return false;
  }
  
  Serial.println("Configuration loaded successfully");
  return true;
}

// Save configuration to EEPROM
bool saveConfiguration(void) {
  Serial.println("Saving configuration to EEPROM...");
  
  // Calculate and set checksum
  currentConfig.checksum = calculateChecksum(&currentConfig);
  
  // Write configuration to EEPROM
  EEPROM.put(0, currentConfig);
  EEPROM.commit();
  
  Serial.println("Configuration saved successfully");
  return true;
}

// Reset configuration to defaults
void resetConfiguration(void) {
  Serial.println("Resetting configuration to defaults...");
  currentConfig = defaultConfig;
  saveConfiguration();
}

// Get PID gains
void getPIDGains(float* p, float* i, float* d) {
  for (int axis = 0; axis < 3; axis++) {
    p[axis] = currentConfig.pidP[axis];
    i[axis] = currentConfig.pidI[axis];
    d[axis] = currentConfig.pidD[axis];
  }
}

// Set PID gains
void setPIDGains(float* p, float* i, float* d) {
  for (int axis = 0; axis < 3; axis++) {
    currentConfig.pidP[axis] = p[axis];
    currentConfig.pidI[axis] = i[axis];
    currentConfig.pidD[axis] = d[axis];
  }
}

// Get gyro calibration
void getGyroCalibration(float* calibration) {
  for (int axis = 0; axis < 3; axis++) {
    calibration[axis] = currentConfig.gyroCalibration[axis];
  }
}

// Set gyro calibration
void setGyroCalibration(float* calibration) {
  for (int axis = 0; axis < 3; axis++) {
    currentConfig.gyroCalibration[axis] = calibration[axis];
  }
}

// Get accelerometer calibration
void getAccCalibration(float* calibration) {
  for (int axis = 0; axis < 3; axis++) {
    calibration[axis] = currentConfig.accCalibration[axis];
  }
}

// Set accelerometer calibration
void setAccCalibration(float* calibration) {
  for (int axis = 0; axis < 3; axis++) {
    currentConfig.accCalibration[axis] = calibration[axis];
  }
}