/*
 * QuadFly Flight Controller - PID Controller Implementation
 * 
 * Professional-grade PID controllers with advanced features
 */

#include "pid_controller.h"

// Global PID controller instances
PIDController_t rollPID;
PIDController_t pitchPID;
PIDController_t yawPID;
PIDController_t altitudePID;
PIDController_t velocityPID;
PIDController_t positionPID;

// Initialize all PID controllers
void initializePIDControllers(void) {
  Serial.println("Initializing PID controllers...");
  
  // Initialize roll PID
  setPIDParams(&rollPID, DEFAULT_ROLL_KP, DEFAULT_ROLL_KI, DEFAULT_ROLL_KD, 100.0f, 400.0f);
  
  // Initialize pitch PID
  setPIDParams(&pitchPID, DEFAULT_PITCH_KP, DEFAULT_PITCH_KI, DEFAULT_PITCH_KD, 100.0f, 400.0f);
  
  // Initialize yaw PID
  setPIDParams(&yawPID, DEFAULT_YAW_KP, DEFAULT_YAW_KI, DEFAULT_YAW_KD, 100.0f, 400.0f);
  
  // Initialize altitude PID
  setPIDParams(&altitudePID, DEFAULT_ALT_KP, DEFAULT_ALT_KI, DEFAULT_ALT_KD, 50.0f, 200.0f);
  
  // Initialize velocity PID
  setPIDParams(&velocityPID, 0.3f, 0.05f, 0.1f, 50.0f, 200.0f);
  
  // Initialize position PID
  setPIDParams(&positionPID, 0.2f, 0.01f, 0.05f, 10.0f, 30.0f);
  
  Serial.println("PID controllers initialized successfully");
}

// Set PID parameters
void setPIDParams(PIDController_t *pid, float kp, float ki, float kd, float integralLimit, float outputLimit) {
  pid->kp = kp;
  pid->ki = ki;
  pid->kd = kd;
  pid->integralLimit = integralLimit;
  pid->outputLimit = outputLimit;
  pid->previousError = 0.0f;
  pid->integral = 0.0f;
  pid->derivative = 0.0f;
  pid->output = 0.0f;
  pid->lastUpdate = 0;
  pid->initialized = false;
}

// Update PID controller
float updatePID(PIDController_t *pid, float setpoint, float measurement, float dt) {
  if (dt <= 0.0f || dt > 0.1f) {
    return pid->output; // Invalid time step
  }
  
  // Calculate error
  float error = setpoint - measurement;
  
  // Proportional term
  float proportional = pid->kp * error;
  
  // Integral term with anti-windup
  pid->integral += error * dt;
  
  // Clamp integral to prevent windup
  if (pid->integral > pid->integralLimit) {
    pid->integral = pid->integralLimit;
  } else if (pid->integral < -pid->integralLimit) {
    pid->integral = -pid->integralLimit;
  }
  
  float integral = pid->ki * pid->integral;
  
  // Derivative term
  if (pid->initialized) {
    pid->derivative = (error - pid->previousError) / dt;
  } else {
    pid->derivative = 0.0f;
    pid->initialized = true;
  }
  
  float derivative = pid->kd * pid->derivative;
  
  // Calculate total output
  pid->output = proportional + integral + derivative;
  
  // Clamp output
  if (pid->output > pid->outputLimit) {
    pid->output = pid->outputLimit;
  } else if (pid->output < -pid->outputLimit) {
    pid->output = -pid->outputLimit;
  }
  
  // Store previous error for next iteration
  pid->previousError = error;
  
  return pid->output;
}

// Reset PID controller
void resetPID(PIDController_t *pid) {
  pid->previousError = 0.0f;
  pid->integral = 0.0f;
  pid->derivative = 0.0f;
  pid->output = 0.0f;
  pid->initialized = false;
}