/*
 * QuadFly Flight Controller - PWM HAL Header
 * 
 * Hardware abstraction for PWM operations
 */

#ifndef HAL_PWM_H
#define HAL_PWM_H

#include "hal.h"

// PWM channel structure
typedef struct {
  uint8_t pin;
  uint8_t channel;
  uint32_t frequency;
  uint8_t resolution;
  uint32_t duty_cycle;
  bool enabled;
} HAL_PWM_Channel;

// PWM configuration structure
typedef struct {
  uint32_t frequency;
  uint8_t resolution;
  bool phase_correct;
} HAL_PWM_TimerConfig;

// Function declarations

// PWM initialization and configuration
HAL_StatusTypeDef HAL_PWM_Init(void);
HAL_StatusTypeDef HAL_PWM_DeInit(void);
HAL_StatusTypeDef HAL_PWM_ConfigChannel(uint8_t channel, const HAL_PWM_Config* config);
HAL_StatusTypeDef HAL_PWM_ConfigTimer(uint8_t timer, const HAL_PWM_TimerConfig* config);

// Channel management
HAL_StatusTypeDef HAL_PWM_AttachPin(uint8_t pin, uint32_t frequency, uint8_t resolution);
HAL_StatusTypeDef HAL_PWM_DetachPin(uint8_t pin);
uint8_t HAL_PWM_GetChannel(uint8_t pin);
bool HAL_PWM_IsChannelUsed(uint8_t channel);

// PWM output control
HAL_StatusTypeDef HAL_PWM_Start(uint8_t channel);
HAL_StatusTypeDef HAL_PWM_Stop(uint8_t channel);
HAL_StatusTypeDef HAL_PWM_SetDutyCycle(uint8_t channel, uint32_t duty_cycle);
HAL_StatusTypeDef HAL_PWM_SetDutyCyclePercent(uint8_t channel, float percent);
HAL_StatusTypeDef HAL_PWM_SetPulseWidth(uint8_t channel, uint32_t pulse_width_us);

// PWM parameter control
HAL_StatusTypeDef HAL_PWM_SetFrequency(uint8_t channel, uint32_t frequency);
HAL_StatusTypeDef HAL_PWM_SetResolution(uint8_t channel, uint8_t resolution);
HAL_StatusTypeDef HAL_PWM_SetPhase(uint8_t channel, uint32_t phase);

// PWM reading
uint32_t HAL_PWM_GetDutyCycle(uint8_t channel);
float HAL_PWM_GetDutyCyclePercent(uint8_t channel);
uint32_t HAL_PWM_GetPulseWidth(uint8_t channel);
uint32_t HAL_PWM_GetFrequency(uint8_t channel);
uint8_t HAL_PWM_GetResolution(uint8_t channel);

// PWM input capture (for RC receiver)
HAL_StatusTypeDef HAL_PWM_StartCapture(uint8_t pin);
HAL_StatusTypeDef HAL_PWM_StopCapture(uint8_t pin);
uint32_t HAL_PWM_GetCapturedPulseWidth(uint8_t pin);
uint32_t HAL_PWM_GetCapturedFrequency(uint8_t pin);
bool HAL_PWM_IsCaptureReady(uint8_t pin);

// Advanced PWM features
HAL_StatusTypeDef HAL_PWM_SetDeadTime(uint8_t channel, uint32_t dead_time_ns);
HAL_StatusTypeDef HAL_PWM_EnableSync(uint8_t* channels, uint8_t count);
HAL_StatusTypeDef HAL_PWM_DisableSync(uint8_t* channels, uint8_t count);
HAL_StatusTypeDef HAL_PWM_TriggerSync(void);

// PWM interrupt handling
typedef void (*HAL_PWM_Callback)(uint8_t channel);
HAL_StatusTypeDef HAL_PWM_EnableInterrupt(uint8_t channel, HAL_PWM_Callback callback);
HAL_StatusTypeDef HAL_PWM_DisableInterrupt(uint8_t channel);

// Utility functions
uint32_t HAL_PWM_CalculateDutyCycle(uint32_t pulse_width_us, uint32_t frequency, uint8_t resolution);
uint32_t HAL_PWM_CalculatePulseWidth(uint32_t duty_cycle, uint32_t frequency, uint8_t resolution);
float HAL_PWM_DutyCycleToPercent(uint32_t duty_cycle, uint8_t resolution);
uint32_t HAL_PWM_PercentToDutyCycle(float percent, uint8_t resolution);

// Channel availability
bool HAL_PWM_IsChannelAvailable(uint8_t channel);
uint8_t HAL_PWM_GetAvailableChannel(void);
uint8_t HAL_PWM_GetMaxChannels(void);

// PWM validation
bool HAL_PWM_IsValidFrequency(uint32_t frequency);
bool HAL_PWM_IsValidResolution(uint8_t resolution);
bool HAL_PWM_IsValidDutyCycle(uint32_t duty_cycle, uint8_t resolution);

#endif // HAL_PWM_H
