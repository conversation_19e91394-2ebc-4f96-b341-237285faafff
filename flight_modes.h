/*
 * QuadFly Flight Controller - Flight Modes Header
 * 
 * Comprehensive flight mode management system
 */

#ifndef FLIGHT_MODES_H
#define FLIGHT_MODES_H

#include "config.h"
#include "pid_controller.h"

// Function declarations
void initializeFlightModes(void);
void processFlightModeTransitions(SystemState_t *state, FlightData_t *flightData);
void executeFlightMode(SystemState_t *state, FlightData_t *flightData);

// Individual flight mode functions
void executeManualMode(SystemState_t *state, FlightData_t *flightData);
void executeStabilizeMode(SystemState_t *state, FlightData_t *flightData);
void executeAltHoldMode(SystemState_t *state, FlightData_t *flightData);
void executeGPSHoldMode(SystemState_t *state, FlightData_t *flightData);
void executeRTHMode(SystemState_t *state, FlightData_t *flightData);
void executeEmergencyMode(SystemState_t *state, FlightData_t *flightData);

// Utility functions
void calculateMotorMix(float throttle, float roll, float pitch, float yaw, uint16_t *motors);
void armingSequenceCheck(SystemState_t *state, FlightData_t *flightData);
void disarmingSequenceCheck(SystemState_t *state, FlightData_t *flightData);

#endif // FLIGHT_MODES_H