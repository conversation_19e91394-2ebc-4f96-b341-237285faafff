/*
 * QuadFly Flight Controller - Calibration System Header
 * 
 * Comprehensive sensor and system calibration routines
 */

#ifndef CALIBRATION_H
#define CALIBRATION_H

#include "config.h"

// Function declarations
void performSystemCalibration(void);
void calibrateAccelerometer(SensorData_t *sensorData);
void calibrateGyroscope(SensorData_t *sensorData);
void calibrateMagnetometer(SensorData_t *sensorData);
void calibrateBarometer(SensorData_t *sensorData);
void calibrateESCs(void);
void saveCalibrationData(SensorData_t *sensorData);
void loadCalibrationData(SensorData_t *sensorData);

#endif // CALIBRATION_H