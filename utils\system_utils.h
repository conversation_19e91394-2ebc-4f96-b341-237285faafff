/*
 * QuadFly Flight Controller - System Utilities Header
 * 
 * System-level utility functions and monitoring
 */

#ifndef SYSTEM_UTILS_H
#define SYSTEM_UTILS_H

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_system.h>
#include <esp_heap_caps.h>

// System monitoring structure
typedef struct {
  uint32_t freeHeap;
  uint32_t minFreeHeap;
  uint32_t totalHeap;
  uint32_t largestFreeBlock;
  float cpuUsage;
  uint32_t uptime;
  uint16_t taskCount;
  uint32_t stackHighWaterMark[10]; // For up to 10 tasks
  bool memoryLeakDetected;
  uint32_t memoryLeakSize;
} SystemMonitor_t;

// Task monitoring structure
typedef struct {
  TaskHandle_t handle;
  char name[16];
  uint32_t stackSize;
  uint32_t freeStack;
  uint8_t priority;
  eTaskState state;
  uint32_t runtime;
  float cpuPercent;
} TaskInfo_t;

// Performance monitoring
typedef struct {
  uint32_t loopCount;
  uint32_t maxLoopTime;
  uint32_t avgLoopTime;
  uint32_t lastLoopTime;
  uint32_t totalLoopTime;
  bool performanceWarning;
} PerformanceMonitor_t;

// System health status
typedef enum {
  SYSTEM_HEALTH_EXCELLENT = 100,
  SYSTEM_HEALTH_GOOD = 80,
  SYSTEM_HEALTH_WARNING = 60,
  SYSTEM_HEALTH_CRITICAL = 40,
  SYSTEM_HEALTH_EMERGENCY = 20,
  SYSTEM_HEALTH_FAILURE = 0
} SystemHealth_t;

// Function declarations

// System monitoring
void initializeSystemMonitoring(void);
void updateSystemMonitor(SystemMonitor_t *monitor);
SystemHealth_t calculateSystemHealth(SystemMonitor_t *monitor);
void printSystemStatus(SystemMonitor_t *monitor);

// Memory management
void* safeMalloc(size_t size);
void safeFree(void* ptr);
void* safeRealloc(void* ptr, size_t size);
bool checkMemoryIntegrity(void);
void detectMemoryLeaks(SystemMonitor_t *monitor);
void printMemoryMap(void);

// Task monitoring
void updateTaskInfo(TaskInfo_t *taskInfo, int maxTasks);
void printTaskStatus(TaskInfo_t *taskInfo, int taskCount);
uint32_t getTaskStackHighWaterMark(TaskHandle_t task);
float calculateTaskCpuUsage(TaskHandle_t task);

// Performance monitoring
void initializePerformanceMonitor(PerformanceMonitor_t *perfMon);
void updatePerformanceMonitor(PerformanceMonitor_t *perfMon, uint32_t loopTime);
void printPerformanceStatus(PerformanceMonitor_t *perfMon);

// CPU usage monitoring
void initializeCpuMonitor(void);
float getCurrentCpuUsage(void);
void updateCpuUsage(SystemMonitor_t *monitor);

// Watchdog functions
void initializeWatchdog(uint32_t timeoutMs);
void feedWatchdog(void);
void disableWatchdog(void);

// Reset and recovery
void softReset(void);
void hardReset(void);
esp_reset_reason_t getResetReason(void);
void printResetReason(void);

// System information
void printSystemInfo(void);
uint32_t getChipId(void);
uint32_t getFlashSize(void);
uint32_t getCpuFrequency(void);
String getMacAddress(void);

// Time utilities
uint32_t getSystemUptime(void);
uint32_t getMillisecondsSinceBoot(void);
uint64_t getMicrosecondsSinceBoot(void);
void delayMicroseconds(uint32_t us);

// String utilities
void safeStringCopy(char *dest, const char *src, size_t destSize);
void safeStringCat(char *dest, const char *src, size_t destSize);
int safeStringCompare(const char *str1, const char *str2);
void stringToUpper(char *str);
void stringToLower(char *str);

// Data validation
bool isValidFloat(float value);
bool isValidRange(float value, float min, float max);
bool isValidPointer(void *ptr);
uint16_t calculateChecksum(uint8_t *data, size_t length);
bool validateChecksum(uint8_t *data, size_t length, uint16_t expectedChecksum);

// Error handling
typedef enum {
  ERROR_NONE = 0,
  ERROR_INVALID_PARAMETER,
  ERROR_OUT_OF_MEMORY,
  ERROR_TIMEOUT,
  ERROR_HARDWARE_FAILURE,
  ERROR_COMMUNICATION_FAILURE,
  ERROR_SENSOR_FAILURE,
  ERROR_CALIBRATION_FAILURE,
  ERROR_CONFIGURATION_INVALID,
  ERROR_SYSTEM_OVERLOAD,
  ERROR_UNKNOWN
} SystemError_t;

void logError(SystemError_t error, const char *message);
void clearError(SystemError_t error);
bool hasError(SystemError_t error);
void printErrorLog(void);

// Debug utilities
#ifdef DEBUG
  #define DEBUG_PRINT(x) Serial.print(x)
  #define DEBUG_PRINTLN(x) Serial.println(x)
  #define DEBUG_PRINTF(format, ...) Serial.printf(format, ##__VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(format, ...)
#endif

// Assert macro
#ifdef DEBUG
  #define ASSERT(condition) \
    do { \
      if (!(condition)) { \
        Serial.printf("ASSERTION FAILED: %s at %s:%d\n", #condition, __FILE__, __LINE__); \
        while(1) delay(1000); \
      } \
    } while(0)
#else
  #define ASSERT(condition)
#endif

// Profiling macros
#ifdef ENABLE_PROFILING
  #define PROFILE_START(name) uint32_t profile_start_##name = micros()
  #define PROFILE_END(name) \
    do { \
      uint32_t profile_time = micros() - profile_start_##name; \
      Serial.printf("PROFILE %s: %u us\n", #name, profile_time); \
    } while(0)
#else
  #define PROFILE_START(name)
  #define PROFILE_END(name)
#endif

#endif // SYSTEM_UTILS_H
