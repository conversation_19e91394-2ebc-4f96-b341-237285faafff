/*
 * QuadFly Flight Controller - GPS Handler Header
 * 
 * NEO-6 GPS module interface and navigation functions
 */

#ifndef GPS_HANDLER_H
#define GPS_HANDLER_H

#include "config.h"

// Function declarations
void initializeGPS(void);
void processGPSData(GPSData_t *gpsData);
bool parseNMEA(String sentence, GPSData_t *gpsData);
float calculateDistance(double lat1, double lon1, double lat2, double lon2);
float calculateBearing(double lat1, double lon1, double lat2, double lon2);

#endif // GPS_HANDLER_H