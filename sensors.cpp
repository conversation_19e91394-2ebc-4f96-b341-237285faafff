/*
 * QuadFly Flight Controller - Sensor Management Implementation
 * 
 * Comprehensive sensor handling with advanced filtering and fusion algorithms
 */

#include "sensors.h"

// Global variables for sensor management
static float quaternion[4] = {1.0f, 0.0f, 0.0f, 0.0f}; // w, x, y, z
static float mahonyKp = 0.5f;    // Proportional gain
static float mahonyKi = 0.0f;    // Integral gain
static float integralFBx = 0.0f, integralFBy = 0.0f, integralFBz = 0.0f;

// MS5611 calibration coefficients
static uint16_t ms5611_C[6];

// Initialize all sensors
bool initializeSensors(void) {
  Serial.println("Initializing sensors...");
  
  // Initialize I2C with slower speed for better compatibility
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz for GY-86 compatibility
  
  delay(100);
  
  bool sensorStatus = true;
  
  // Initialize GY-86 module (MPU6050 + HMC5883L)
  if (initializeGY86Module()) {
    Serial.println("GY-86 module (MPU6050 + HMC5883L) initialized successfully");
  } else {
    Serial.println("ERROR: GY-86 module initialization failed");
    sensorStatus = false;
  }
  
  // Initialize MS5611 barometer (directly connected to I2C)
  if (initializeMS5611()) {
    Serial.println("MS5611 barometer connected successfully");
  } else {
    Serial.println("ERROR: MS5611 initialization failed");
    sensorStatus = false;
  }
  
  if (sensorStatus) {
    Serial.println("All sensors initialized successfully");
    return true;
  } else {
    Serial.println("ERROR: Critical sensor initialization failed!");
    return false;
  }
}

// Initialize GY-86 module with proper bypass sequence
bool initializeGY86Module(void) {
  Serial.println("Initializing GY-86 module...");
  
  // Step 1: Initialize MPU6050 first
  if (!initializeMPU6050()) {
    Serial.println("ERROR: MPU6050 initialization failed");
    return false;
  }
  
  // Step 2: Enable bypass mode to access HMC5883L
  if (!setMPU6050BypassMode(true)) {
    Serial.println("ERROR: Failed to enable bypass mode");
    return false;
  }
  
  delay(50); // Wait for bypass mode to activate
  
  // Step 3: Initialize HMC5883L while in bypass mode
  bool hmc5883lOk = false;
  for (int retry = 0; retry < 3; retry++) {
    Serial.print("Attempting HMC5883L initialization (attempt ");
    Serial.print(retry + 1);
    Serial.println(")...");
    
    if (initializeHMC5883L()) {
      Serial.println("HMC5883L initialized successfully");
      hmc5883lOk = true;
      break;
    }
    delay(200);
  }
  
  // Step 4: Disable bypass mode and configure auxiliary I2C
  if (!setMPU6050BypassMode(false)) {
    Serial.println("ERROR: Failed to disable bypass mode");
    return false;
  }
  
  delay(50);
  
  // Step 5: Configure MPU6050 to automatically read HMC5883L
  if (hmc5883lOk) {
    if (!configureMPU6050AuxI2C()) {
      Serial.println("WARNING: Failed to configure auxiliary I2C - using bypass mode");
      setMPU6050BypassMode(true); // Fall back to bypass mode
    }
  }
  
  return hmc5883lOk; // Return true if at least HMC5883L works
}

// Set MPU6050 bypass mode
bool setMPU6050BypassMode(bool enable) {
  Serial.print("Setting MPU6050 bypass mode: ");
  Serial.println(enable ? "ENABLED" : "DISABLED");
  
  if (enable) {
    // Enable bypass mode
    // 1. Disable I2C master mode
    uint8_t userCtrl = readMPU6050Register(MPU6050_RA_USER_CTRL);
    userCtrl &= ~(1 << MPU6050_USERCTRL_I2C_MST_EN_BIT);
    if (!writeMPU6050Register(MPU6050_RA_USER_CTRL, userCtrl)) {
      return false;
    }
    
    delay(10);
    
    // 2. Enable bypass mode
    uint8_t intPinCfg = readMPU6050Register(MPU6050_RA_INT_PIN_CFG);
    intPinCfg |= (1 << MPU6050_INTCFG_I2C_BYPASS_EN_BIT);
    if (!writeMPU6050Register(MPU6050_RA_INT_PIN_CFG, intPinCfg)) {
      return false;
    }
  } else {
    // Disable bypass mode
    // 1. Disable bypass mode
    uint8_t intPinCfg = readMPU6050Register(MPU6050_RA_INT_PIN_CFG);
    intPinCfg &= ~(1 << MPU6050_INTCFG_I2C_BYPASS_EN_BIT);
    if (!writeMPU6050Register(MPU6050_RA_INT_PIN_CFG, intPinCfg)) {
      return false;
    }
    
    delay(10);
    
    // 2. Enable I2C master mode
    uint8_t userCtrl = readMPU6050Register(MPU6050_RA_USER_CTRL);
    userCtrl |= (1 << MPU6050_USERCTRL_I2C_MST_EN_BIT);
    if (!writeMPU6050Register(MPU6050_RA_USER_CTRL, userCtrl)) {
      return false;
    }
  }
  
  delay(50);
  return true;
}

// Configure MPU6050 auxiliary I2C to automatically read HMC5883L
bool configureMPU6050AuxI2C(void) {
  Serial.println("Configuring MPU6050 auxiliary I2C for HMC5883L...");
  
  // This is advanced configuration - for now, we'll use bypass mode
  // In a full implementation, you would configure:
  // - I2C_SLV0_ADDR register (0x25) = HMC5883L_ADDRESS | 0x80 (read bit)
  // - I2C_SLV0_REG register (0x26) = 0x03 (HMC5883L data register)
  // - I2C_SLV0_CTRL register (0x27) = 0x86 (enable + 6 bytes)
  // - I2C_MST_CTRL register (0x24) = 0x0D (400kHz)
  
  // For simplicity, we'll keep bypass mode enabled
  Serial.println("Using bypass mode for magnetometer access");
  return setMPU6050BypassMode(true);
}

// Write to MPU6050 register
bool writeMPU6050Register(uint8_t reg, uint8_t data) {
  Wire.beginTransmission(MPU6050_ADDRESS);
  Wire.write(reg);
  Wire.write(data);
  return (Wire.endTransmission() == 0);
}

// Read from MPU6050 register
uint8_t readMPU6050Register(uint8_t reg) {
  Wire.beginTransmission(MPU6050_ADDRESS);
  Wire.write(reg);
  Wire.endTransmission(false);
  
  Wire.requestFrom(MPU6050_ADDRESS, 1);
  if (Wire.available()) {
    return Wire.read();
  }
  return 0;
}

// Updated HMC5883L initialization for GY-86
bool initializeHMC5883L(void) {
  Serial.println("Initializing HMC5883L magnetometer...");
  
  // Check if device is present
  Wire.beginTransmission(HMC5883L_ADDRESS);
  if (Wire.endTransmission() != 0) {
    Serial.println("HMC5883L not found on I2C bus");
    return false;
  }
  
  delay(50);
  
  // Set configuration register A (0x00)
  Wire.beginTransmission(HMC5883L_ADDRESS);
  Wire.write(0x00); // Configuration Register A
  Wire.write(0x70); // 8-average, 15Hz default, normal measurement
  if (Wire.endTransmission() != 0) {
    Serial.println("Failed to write HMC5883L config register A");
    return false;
  }
  
  delay(10);
  
  // Set configuration register B (0x01)
  Wire.beginTransmission(HMC5883L_ADDRESS);
  Wire.write(0x01); // Configuration Register B
  Wire.write(0x20); // Gain = 1.3 Ga (default)
  if (Wire.endTransmission() != 0) {
    Serial.println("Failed to write HMC5883L config register B");
    return false;
  }
  
  delay(10);
  
  // Set mode register (0x02)
  Wire.beginTransmission(HMC5883L_ADDRESS);
  Wire.write(0x02); // Mode Register
  Wire.write(0x00); // Continuous measurement mode
  if (Wire.endTransmission() != 0) {
    Serial.println("Failed to write HMC5883L mode register");
    return false;
  }
  
  delay(100); // Wait for first measurement
  
  // Test read to verify communication
  Wire.beginTransmission(HMC5883L_ADDRESS);
  Wire.write(0x03); // Data output X MSB register
  if (Wire.endTransmission() != 0) {
    Serial.println("Failed to request HMC5883L data");
    return false;
  }
  
  if (Wire.requestFrom(HMC5883L_ADDRESS, 6) != 6) {
    Serial.println("Failed to read HMC5883L data");
    return false;
  }
  
  // Read and discard test data
  for (int i = 0; i < 6; i++) {
    Wire.read();
  }
  
  Serial.println("HMC5883L initialized successfully");
  return true;
}

// Initialize MS5611 barometric pressure sensor
bool initializeMS5611(void) {
  // Reset sensor
  Wire.beginTransmission(MS5611_ADDRESS);
  Wire.write(0x1E); // Reset command
  if (Wire.endTransmission() != 0) {
    return false;
  }
  
  delay(10);
  
  // Read calibration coefficients
  for (int i = 0; i < 6; i++) {
    Wire.beginTransmission(MS5611_ADDRESS);
    Wire.write(0xA2 + (i * 2)); // Read PROM
    Wire.endTransmission();
    Wire.requestFrom(MS5611_ADDRESS, 2);
    
    if (Wire.available() >= 2) {
      ms5611_C[i] = (Wire.read() << 8) | Wire.read();
    } else {
      return false;
    }
  }
  
  Serial.println("MS5611 connected successfully");
  return true;
}

// Read all sensor data
void readSensors(SensorData_t *sensorData) {
  readMPU6050(sensorData);
  readHMC5883L(sensorData);
  readMS5611(sensorData);
  
  sensorData->dataValid = true;
  sensorData->lastUpdate = millis();
}

// Read MPU6050 data
void readMPU6050(SensorData_t *sensorData) {
  Wire.beginTransmission(MPU6050_ADDRESS);
  Wire.write(0x3B); // Start reading from ACCEL_XOUT_H
  Wire.endTransmission();
  Wire.requestFrom(MPU6050_ADDRESS, 14);
  
  if (Wire.available() >= 14) {
    // Read accelerometer data
    int16_t accelX_raw = (Wire.read() << 8) | Wire.read();
    int16_t accelY_raw = (Wire.read() << 8) | Wire.read();
    int16_t accelZ_raw = (Wire.read() << 8) | Wire.read();
    
    // Read temperature
    int16_t temp_raw = (Wire.read() << 8) | Wire.read();
    
    // Read gyroscope data
    int16_t gyroX_raw = (Wire.read() << 8) | Wire.read();
    int16_t gyroY_raw = (Wire.read() << 8) | Wire.read();
    int16_t gyroZ_raw = (Wire.read() << 8) | Wire.read();
    
    // Convert to physical units
    sensorData->accelX = ((float)accelX_raw / 4096.0f) * 9.81f - sensorData->accelOffset[0];
    sensorData->accelY = ((float)accelY_raw / 4096.0f) * 9.81f - sensorData->accelOffset[1];
    sensorData->accelZ = ((float)accelZ_raw / 4096.0f) * 9.81f - sensorData->accelOffset[2];
    
    sensorData->gyroX = ((float)gyroX_raw / 32.8f) * DEG_TO_RAD - sensorData->gyroOffset[0];
    sensorData->gyroY = ((float)gyroY_raw / 32.8f) * DEG_TO_RAD - sensorData->gyroOffset[1];
    sensorData->gyroZ = ((float)gyroZ_raw / 32.8f) * DEG_TO_RAD - sensorData->gyroOffset[2];
    
    sensorData->temperature = (float)temp_raw / 340.0f + 36.53f;
  }
}

// Read HMC5883L data
void readHMC5883L(SensorData_t *sensorData) {
  Wire.beginTransmission(HMC5883L_ADDRESS);
  Wire.write(0x03); // Start reading from DXRA
  Wire.endTransmission();
  Wire.requestFrom(HMC5883L_ADDRESS, 6);
  
  if (Wire.available() >= 6) {
    int16_t magX_raw = (Wire.read() << 8) | Wire.read();
    int16_t magZ_raw = (Wire.read() << 8) | Wire.read(); // Note: Z comes before Y
    int16_t magY_raw = (Wire.read() << 8) | Wire.read();
    
    // Apply calibration offsets and scaling
    sensorData->magX = ((float)magX_raw - sensorData->magOffset[0]) * sensorData->magScale[0];
    sensorData->magY = ((float)magY_raw - sensorData->magOffset[1]) * sensorData->magScale[1];
    sensorData->magZ = ((float)magZ_raw - sensorData->magOffset[2]) * sensorData->magScale[2];
  }
}

// Read MS5611 data
void readMS5611(SensorData_t *sensorData) {
  static bool temperatureReading = true;
  static uint32_t lastConversion = 0;
  static uint32_t D1 = 0, D2 = 0; // Digital pressure and temperature values
  
  uint32_t currentTime = millis();
  
  if (currentTime - lastConversion >= 10) { // 10ms conversion time
    if (temperatureReading) {
      // Read temperature conversion result
      Wire.beginTransmission(MS5611_ADDRESS);
      Wire.write(0x00); // ADC Read
      Wire.endTransmission();
      Wire.requestFrom(MS5611_ADDRESS, 3);
      
      if (Wire.available() >= 3) {
        D2 = ((uint32_t)Wire.read() << 16) | ((uint32_t)Wire.read() << 8) | Wire.read();
      }
      
      // Start pressure conversion
      Wire.beginTransmission(MS5611_ADDRESS);
      Wire.write(0x48); // Convert D1 (pressure) OSR=4096
      Wire.endTransmission();
      
      temperatureReading = false;
    } else {
      // Read pressure conversion result
      Wire.beginTransmission(MS5611_ADDRESS);
      Wire.write(0x00); // ADC Read
      Wire.endTransmission();
      Wire.requestFrom(MS5611_ADDRESS, 3);
      
      if (Wire.available() >= 3) {
        D1 = ((uint32_t)Wire.read() << 16) | ((uint32_t)Wire.read() << 8) | Wire.read();
      }
      
      // Calculate temperature and pressure
      int64_t dT = D2 - ((uint32_t)ms5611_C[4] << 8);
      int32_t TEMP = 2000 + ((dT * ms5611_C[5]) >> 23);
      
      int64_t OFF = ((int64_t)ms5611_C[1] << 16) + (((int64_t)ms5611_C[3] * dT) >> 7);
      int64_t SENS = ((int64_t)ms5611_C[0] << 15) + (((int64_t)ms5611_C[2] * dT) >> 8);
      
      int32_t P = (((D1 * SENS) >> 21) - OFF) >> 15;
      
      sensorData->pressure = (float)P;
      
      // Calculate altitude from pressure (using standard atmosphere model)
      sensorData->baroAltitude = 44330.0f * (1.0f - pow(P / 101325.0f, 0.1903f));
      
      // Start temperature conversion
      Wire.beginTransmission(MS5611_ADDRESS);
      Wire.write(0x58); // Convert D2 (temperature) OSR=4096
      Wire.endTransmission();
      
      temperatureReading = true;
    }
    
    lastConversion = currentTime;
  }
}

// Update attitude estimation using Mahony AHRS algorithm
void updateAttitudeEstimation(SensorData_t *sensors, AttitudeData_t *attitude) {
  static uint32_t lastUpdate = 0;
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  if (dt > 0.0f && dt < 0.1f) { // Valid time step
    // Run Mahony AHRS update
    mahonyAHRSUpdate(
      sensors->gyroX, sensors->gyroY, sensors->gyroZ,
      sensors->accelX, sensors->accelY, sensors->accelZ,
      sensors->magX, sensors->magY, sensors->magZ,
      quaternion
    );
    
    // Convert quaternion to Euler angles
    quaternionToEuler(quaternion, &attitude->roll, &attitude->pitch, &attitude->yaw);
    
    // Store quaternion
    for (int i = 0; i < 4; i++) {
      attitude->quaternion[i] = quaternion[i];
    }
    
    // Calculate angular rates
    attitude->rollRate = sensors->gyroX;
    attitude->pitchRate = sensors->gyroY;
    attitude->yawRate = sensors->gyroZ;
  }
  
  lastUpdate = currentTime;
}

// Mahony AHRS algorithm implementation
void mahonyAHRSUpdate(float gx, float gy, float gz, float ax, float ay, float az, 
                      float mx, float my, float mz, float *q) {
  float recipNorm;
  float q0q0, q0q1, q0q2, q0q3, q1q1, q1q2, q1q3, q2q2, q2q3, q3q3;
  float hx, hy, bx, bz;
  float halfvx, halfvy, halfvz, halfwx, halfwy, halfwz;
  float halfex, halfey, halfez;
  float qa, qb, qc;
  
  static float dt = 0.005f; // 5ms
  
  // Use IMU algorithm if magnetometer measurement invalid
  if((mx == 0.0f) && (my == 0.0f) && (mz == 0.0f)) {
    // IMU algorithm (without magnetometer)
    
    // Normalise accelerometer measurement
    recipNorm = 1.0f / sqrt(ax * ax + ay * ay + az * az);
    ax *= recipNorm;
    ay *= recipNorm;
    az *= recipNorm;
    
    // Estimated direction of gravity and vector perpendicular to magnetic flux
    halfvx = q[1] * q[3] - q[0] * q[2];
    halfvy = q[0] * q[1] + q[2] * q[3];
    halfvz = q[0] * q[0] - 0.5f + q[3] * q[3];
    
    // Error is sum of cross product between estimated and measured direction of gravity
    halfex = (ay * halfvz - az * halfvy);
    halfey = (az * halfvx - ax * halfvz);
    halfez = (ax * halfvy - ay * halfvx);
    
    // Compute and apply integral feedback if enabled
    if(mahonyKi > 0.0f) {
      integralFBx += mahonyKi * halfex * dt;
      integralFBy += mahonyKi * halfey * dt;
      integralFBz += mahonyKi * halfez * dt;
      gx += integralFBx;
      gy += integralFBy;
      gz += integralFBz;
    } else {
      integralFBx = 0.0f;
      integralFBy = 0.0f;
      integralFBz = 0.0f;
    }
    
    // Apply proportional feedback
    gx += mahonyKp * halfex;
    gy += mahonyKp * halfey;
    gz += mahonyKp * halfez;
  } else {
    // MARG algorithm (with magnetometer)
    
    // Normalise accelerometer measurement
    recipNorm = 1.0f / sqrt(ax * ax + ay * ay + az * az);
    ax *= recipNorm;
    ay *= recipNorm;
    az *= recipNorm;
    
    // Normalise magnetometer measurement
    recipNorm = 1.0f / sqrt(mx * mx + my * my + mz * mz);
    mx *= recipNorm;
    my *= recipNorm;
    mz *= recipNorm;
    
    // Auxiliary variables to avoid repeated arithmetic
    q0q0 = q[0] * q[0];
    q0q1 = q[0] * q[1];
    q0q2 = q[0] * q[2];
    q0q3 = q[0] * q[3];
    q1q1 = q[1] * q[1];
    q1q2 = q[1] * q[2];
    q1q3 = q[1] * q[3];
    q2q2 = q[2] * q[2];
    q2q3 = q[2] * q[3];
    q3q3 = q[3] * q[3];
    
    // Reference direction of Earth's magnetic field
    hx = 2.0f * (mx * (0.5f - q2q2 - q3q3) + my * (q1q2 - q0q3) + mz * (q1q3 + q0q2));
    hy = 2.0f * (mx * (q1q2 + q0q3) + my * (0.5f - q1q1 - q3q3) + mz * (q2q3 - q0q1));
    bx = sqrt(hx * hx + hy * hy);
    bz = 2.0f * (mx * (q1q3 - q0q2) + my * (q2q3 + q0q1) + mz * (0.5f - q1q1 - q2q2));
    
    // Estimated direction of gravity and magnetic field
    halfvx = q1q3 - q0q2;
    halfvy = q0q1 + q2q3;
    halfvz = q0q0 - 0.5f + q3q3;
    halfwx = bx * (0.5f - q2q2 - q3q3) + bz * (q1q3 - q0q2);
    halfwy = bx * (q1q2 - q0q3) + bz * (q0q1 + q2q3);
    halfwz = bx * (q0q2 + q1q3) + bz * (0.5f - q1q1 - q2q2);
    
    // Error is sum of cross product between estimated direction and measured direction of field vectors
    halfex = (ay * halfvz - az * halfvy) + (my * halfwz - mz * halfwy);
    halfey = (az * halfvx - ax * halfvz) + (mz * halfwx - mx * halfwz);
    halfez = (ax * halfvy - ay * halfvx) + (mx * halfwy - my * halfwx);
    
    // Compute and apply integral feedback if enabled
    if(mahonyKi > 0.0f) {
      integralFBx += mahonyKi * halfex * dt;
      integralFBy += mahonyKi * halfey * dt;
      integralFBz += mahonyKi * halfez * dt;
      gx += integralFBx;
      gy += integralFBy;
      gz += integralFBz;
    } else {
      integralFBx = 0.0f;
      integralFBy = 0.0f;
      integralFBz = 0.0f;
    }
    
    // Apply proportional feedback
    gx += mahonyKp * halfex;
    gy += mahonyKp * halfey;
    gz += mahonyKp * halfez;
  }
  
  // Integrate rate of change of quaternion
  gx *= (0.5f * dt);
  gy *= (0.5f * dt);
  gz *= (0.5f * dt);
  qa = q[0];
  qb = q[1];
  qc = q[2];
  q[0] += (-qb * gx - qc * gy - q[3] * gz);
  q[1] += (qa * gx + qc * gz - q[3] * gy);
  q[2] += (qa * gy - qb * gz + q[3] * gx);
  q[3] += (qa * gz + qb * gy - qc * gx);
  
  // Normalise quaternion
  recipNorm = 1.0f / sqrt(q[0] * q[0] + q[1] * q[1] + q[2] * q[2] + q[3] * q[3]);
  q[0] *= recipNorm;
  q[1] *= recipNorm;
  q[2] *= recipNorm;
  q[3] *= recipNorm;
}

// Convert quaternion to Euler angles
void quaternionToEuler(float *q, float *roll, float *pitch, float *yaw) {
  float sinr_cosp = 2 * (q[0] * q[1] + q[2] * q[3]);
  float cosr_cosp = 1 - 2 * (q[1] * q[1] + q[2] * q[2]);
  *roll = atan2(sinr_cosp, cosr_cosp);
  
  float sinp = 2 * (q[0] * q[2] - q[3] * q[1]);
  if (abs(sinp) >= 1)
    *pitch = copysign(PI / 2, sinp);
  else
    *pitch = asin(sinp);
  
  float siny_cosp = 2 * (q[0] * q[3] + q[1] * q[2]);
  float cosy_cosp = 1 - 2 * (q[2] * q[2] + q[3] * q[3]);
  *yaw = atan2(siny_cosp, cosy_cosp);
}

// Update altitude estimation
void updateAltitudeEstimation(SensorData_t *sensors, AltitudeData_t *altitude) {
  static float previousAltitude = 0.0f;
  static uint32_t lastUpdate = 0;
  static bool initialized = false;
  static float baseAltitude = 0.0f;
  
  uint32_t currentTime = millis();
  float dt = (currentTime - lastUpdate) / 1000.0f;
  
  // Initialize base altitude on first reading
  if (!initialized && sensors->baroAltitude != 0.0f) {
    baseAltitude = sensors->baroAltitude;
    initialized = true;
    Serial.print("Base altitude set to: ");
    Serial.println(baseAltitude);
  }
  
  if (initialized) {
    // Calculate relative altitude
    altitude->pressureAltitude = sensors->baroAltitude - baseAltitude;
    altitude->altitude = altitude->pressureAltitude; // Primary altitude source
    
    // Calculate vertical speed
    if (dt > 0.0f && dt < 0.1f) {
      altitude->verticalSpeed = (altitude->altitude - previousAltitude) / dt;
      previousAltitude = altitude->altitude;
    }
  }
  
  lastUpdate = currentTime;
}

// Update system health monitoring
void updateSystemHealth(SystemState_t *state) {
  state->systemHealth = 0;
  
  // Check IMU health
  state->systemHealth |= HEALTH_IMU_OK;
  
  // Check barometer health
  state->systemHealth |= HEALTH_BARO_OK;
  
  // Check magnetometer health
  state->systemHealth |= HEALTH_MAG_OK;
  
  // Additional health checks would be implemented here
}

// Try both HMC5883L and QMC5883L
bool initializeMagnetometer(void) {
  // First try HMC5883L
  Wire.beginTransmission(HMC5883L_ADDRESS);
  if (Wire.endTransmission() == 0) {
    Serial.println("Detected HMC5883L magnetometer");
    return initializeHMC5883L();
  }
  
  // Then try QMC5883L
  Wire.beginTransmission(QMC5883L_ADDRESS);
  if (Wire.endTransmission() == 0) {
    Serial.println("Detected QMC5883L magnetometer");
    return initializeQMC5883L();
  }
  
  Serial.println("No magnetometer detected");
  return false;
}

bool initializeQMC5883L(void) {
  // Reset the chip
  Wire.beginTransmission(QMC5883L_ADDRESS);
  Wire.write(0x0A); // Control register 1
  Wire.write(0x80); // Software reset
  Wire.endTransmission();
  delay(100);
  
  // Set the chip to continuous mode
  Wire.beginTransmission(QMC5883L_ADDRESS);
  Wire.write(0x09); // Control register 2
  Wire.write(0x01); // Enable interrupt
  Wire.endTransmission();
  
  Wire.beginTransmission(QMC5883L_ADDRESS);
  Wire.write(0x0A); // Control register 1
  Wire.write(0x01); // Continuous mode, 10Hz, 2G, 512 OSR
  Wire.endTransmission();
  
  delay(100);
  return true;
}

// Initialize MPU6050 IMU sensor
bool initializeMPU6050(void) {
  Serial.println("Initializing MPU6050 IMU...");
  
  // Check if device is present
  Wire.beginTransmission(MPU6050_ADDRESS);
  if (Wire.endTransmission() != 0) {
    Serial.println("MPU6050 not found on I2C bus");
    return false;
  }
  
  delay(100);
  
  // Wake up the MPU6050 (it starts in sleep mode)
  if (!writeMPU6050Register(0x6B, 0x00)) { // PWR_MGMT_1 register
    Serial.println("Failed to wake up MPU6050");
    return false;
  }
  
  delay(50);
  
  // Configure accelerometer (+/-8g range)
  if (!writeMPU6050Register(0x1C, 0x10)) { // ACCEL_CONFIG register
    Serial.println("Failed to configure MPU6050 accelerometer");
    return false;
  }
  
  // Configure gyroscope (+/-1000 deg/s range)
  if (!writeMPU6050Register(0x1B, 0x10)) { // GYRO_CONFIG register
    Serial.println("Failed to configure MPU6050 gyroscope");
    return false;
  }
  
  // Set sample rate to 200Hz (1kHz / (1 + 4) = 200Hz)
  if (!writeMPU6050Register(0x19, 0x04)) { // SMPRT_DIV register
    Serial.println("Failed to set MPU6050 sample rate");
    return false;
  }
  
  // Configure digital low pass filter (DLPF = 3, ~43Hz bandwidth)
  if (!writeMPU6050Register(0x1A, 0x03)) { // CONFIG register
    Serial.println("Failed to configure MPU6050 DLPF");
    return false;
  }
  
  delay(100);
  
  // Test read to verify communication
  Wire.beginTransmission(MPU6050_ADDRESS);
  Wire.write(0x75); // WHO_AM_I register
  if (Wire.endTransmission() != 0) {
    Serial.println("Failed to request MPU6050 WHO_AM_I");
    return false;
  }
  
  if (Wire.requestFrom(MPU6050_ADDRESS, 1) != 1) {
    Serial.println("Failed to read MPU6050 WHO_AM_I");
    return false;
  }
  
  uint8_t whoAmI = Wire.read();
  if (whoAmI != 0x68) {
    Serial.print("MPU6050 WHO_AM_I mismatch. Expected 0x68, got 0x");
    Serial.println(whoAmI, HEX);
    return false;
  }
  
  Serial.println("MPU6050 initialized successfully");
  return true;
}