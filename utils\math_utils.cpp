/*
 * QuadFly Flight Controller - Math Utilities Implementation
 * 
 * Common mathematical functions and utilities
 */

#include "math_utils.h"

// Basic math functions
float constrainFloat(float value, float min, float max) {
  if (value < min) return min;
  if (value > max) return max;
  return value;
}

float mapFloat(float value, float in_min, float in_max, float out_min, float out_max) {
  return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

float deadbandFloat(float value, float deadband) {
  if (fabs(value) < deadband) return 0.0f;
  return value > 0 ? value - deadband : value + deadband;
}

float lowPassFilter(float current, float previous, float alpha) {
  return alpha * current + (1.0f - alpha) * previous;
}

float highPassFilter(float current, float previous, float alpha) {
  return alpha * (previous + current - previous);
}

float complementaryFilter(float angle, float gyro_rate, float dt, float alpha) {
  return alpha * (angle + gyro_rate * dt) + (1.0f - alpha) * angle;
}

// Vector operations
Vector3_t vectorAdd(Vector3_t a, Vector3_t b) {
  Vector3_t result = {a.x + b.x, a.y + b.y, a.z + b.z};
  return result;
}

Vector3_t vectorSubtract(Vector3_t a, Vector3_t b) {
  Vector3_t result = {a.x - b.x, a.y - b.y, a.z - b.z};
  return result;
}

Vector3_t vectorScale(Vector3_t v, float scale) {
  Vector3_t result = {v.x * scale, v.y * scale, v.z * scale};
  return result;
}

float vectorMagnitude(Vector3_t v) {
  return sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
}

Vector3_t vectorNormalize(Vector3_t v) {
  float mag = vectorMagnitude(v);
  if (mag > 0.0f) {
    Vector3_t result = {v.x / mag, v.y / mag, v.z / mag};
    return result;
  }
  Vector3_t zero = {0.0f, 0.0f, 0.0f};
  return zero;
}

float vectorDotProduct(Vector3_t a, Vector3_t b) {
  return a.x * b.x + a.y * b.y + a.z * b.z;
}

Vector3_t vectorCrossProduct(Vector3_t a, Vector3_t b) {
  Vector3_t result = {
    a.y * b.z - a.z * b.y,
    a.z * b.x - a.x * b.z,
    a.x * b.y - a.y * b.x
  };
  return result;
}

// Quaternion operations
Quaternion_t quaternionMultiply(Quaternion_t a, Quaternion_t b) {
  Quaternion_t result = {
    a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z,
    a.w * b.x + a.x * b.w + a.y * b.z - a.z * b.y,
    a.w * b.y - a.x * b.z + a.y * b.w + a.z * b.x,
    a.w * b.z + a.x * b.y - a.y * b.x + a.z * b.w
  };
  return result;
}

Quaternion_t quaternionNormalize(Quaternion_t q) {
  float norm = sqrt(q.w * q.w + q.x * q.x + q.y * q.y + q.z * q.z);
  if (norm > 0.0f) {
    Quaternion_t result = {q.w / norm, q.x / norm, q.y / norm, q.z / norm};
    return result;
  }
  Quaternion_t identity = {1.0f, 0.0f, 0.0f, 0.0f};
  return identity;
}

Quaternion_t quaternionConjugate(Quaternion_t q) {
  Quaternion_t result = {q.w, -q.x, -q.y, -q.z};
  return result;
}

void quaternionToEulerAngles(Quaternion_t q, float *roll, float *pitch, float *yaw) {
  // Roll (x-axis rotation)
  float sinr_cosp = 2 * (q.w * q.x + q.y * q.z);
  float cosr_cosp = 1 - 2 * (q.x * q.x + q.y * q.y);
  *roll = atan2(sinr_cosp, cosr_cosp);

  // Pitch (y-axis rotation)
  float sinp = 2 * (q.w * q.y - q.z * q.x);
  if (fabs(sinp) >= 1)
    *pitch = copysign(PI_F / 2, sinp); // Use 90 degrees if out of range
  else
    *pitch = asin(sinp);

  // Yaw (z-axis rotation)
  float siny_cosp = 2 * (q.w * q.z + q.x * q.y);
  float cosy_cosp = 1 - 2 * (q.y * q.y + q.z * q.z);
  *yaw = atan2(siny_cosp, cosy_cosp);
}

Quaternion_t eulerToQuaternion(float roll, float pitch, float yaw) {
  float cr = cos(roll * 0.5f);
  float sr = sin(roll * 0.5f);
  float cp = cos(pitch * 0.5f);
  float sp = sin(pitch * 0.5f);
  float cy = cos(yaw * 0.5f);
  float sy = sin(yaw * 0.5f);

  Quaternion_t q = {
    cr * cp * cy + sr * sp * sy,
    sr * cp * cy - cr * sp * sy,
    cr * sp * cy + sr * cp * sy,
    cr * cp * sy - sr * sp * cy
  };
  return q;
}

// Angle utilities
float normalizeAngle(float angle) {
  while (angle > PI_F) angle -= 2.0f * PI_F;
  while (angle < -PI_F) angle += 2.0f * PI_F;
  return angle;
}

float angleDifference(float angle1, float angle2) {
  float diff = angle2 - angle1;
  return normalizeAngle(diff);
}

float wrapAngle180(float angle) {
  while (angle > 180.0f) angle -= 360.0f;
  while (angle < -180.0f) angle += 360.0f;
  return angle;
}

float wrapAngle360(float angle) {
  while (angle >= 360.0f) angle -= 360.0f;
  while (angle < 0.0f) angle += 360.0f;
  return angle;
}

// Statistical functions
float calculateMean(float *data, int count) {
  if (count <= 0) return 0.0f;
  float sum = 0.0f;
  for (int i = 0; i < count; i++) {
    sum += data[i];
  }
  return sum / count;
}

float calculateVariance(float *data, int count) {
  if (count <= 1) return 0.0f;
  float mean = calculateMean(data, count);
  float sum = 0.0f;
  for (int i = 0; i < count; i++) {
    float diff = data[i] - mean;
    sum += diff * diff;
  }
  return sum / (count - 1);
}

float calculateStandardDeviation(float *data, int count) {
  return sqrt(calculateVariance(data, count));
}

// Interpolation functions
float linearInterpolation(float x0, float y0, float x1, float y1, float x) {
  if (x1 == x0) return y0;
  return y0 + (y1 - y0) * (x - x0) / (x1 - x0);
}

float cubicInterpolation(float p0, float p1, float p2, float p3, float t) {
  float a = -0.5f * p0 + 1.5f * p1 - 1.5f * p2 + 0.5f * p3;
  float b = p0 - 2.5f * p1 + 2.0f * p2 - 0.5f * p3;
  float c = -0.5f * p0 + 0.5f * p2;
  float d = p1;
  
  return a * t * t * t + b * t * t + c * t + d;
}

// Coordinate transformations
void bodyToEarthFrame(Vector3_t body, float roll, float pitch, float yaw, Vector3_t *earth) {
  float cr = cos(roll), sr = sin(roll);
  float cp = cos(pitch), sp = sin(pitch);
  float cy = cos(yaw), sy = sin(yaw);
  
  earth->x = (cy * cp) * body.x + (cy * sp * sr - sy * cr) * body.y + (cy * sp * cr + sy * sr) * body.z;
  earth->y = (sy * cp) * body.x + (sy * sp * sr + cy * cr) * body.y + (sy * sp * cr - cy * sr) * body.z;
  earth->z = (-sp) * body.x + (cp * sr) * body.y + (cp * cr) * body.z;
}

void earthToBodyFrame(Vector3_t earth, float roll, float pitch, float yaw, Vector3_t *body) {
  float cr = cos(roll), sr = sin(roll);
  float cp = cos(pitch), sp = sin(pitch);
  float cy = cos(yaw), sy = sin(yaw);
  
  body->x = (cy * cp) * earth.x + (sy * cp) * earth.y + (-sp) * earth.z;
  body->y = (cy * sp * sr - sy * cr) * earth.x + (sy * sp * sr + cy * cr) * earth.y + (cp * sr) * earth.z;
  body->z = (cy * sp * cr + sy * sr) * earth.x + (sy * sp * cr - cy * sr) * earth.y + (cp * cr) * earth.z;
}

// Fast math approximations
float fastSin(float x) {
  x = normalizeAngle(x);
  if (x < 0) {
    return 1.27323954f * x + 0.405284735f * x * x;
  } else {
    return 1.27323954f * x - 0.405284735f * x * x;
  }
}

float fastCos(float x) {
  return fastSin(x + PI_F / 2.0f);
}

float fastAtan2(float y, float x) {
  if (x == 0.0f) {
    if (y > 0.0f) return PI_F / 2.0f;
    if (y < 0.0f) return -PI_F / 2.0f;
    return 0.0f;
  }
  
  float atan = y / x;
  if (fabs(atan) < 1.0f) {
    atan = atan / (1.0f + 0.28f * atan * atan);
    if (x < 0.0f) {
      if (y < 0.0f) return atan - PI_F;
      return atan + PI_F;
    }
  } else {
    atan = PI_F / 2.0f - atan / (atan * atan + 0.28f);
    if (y < 0.0f) return atan - PI_F;
  }
  return atan;
}

float fastSqrt(float x) {
  if (x <= 0.0f) return 0.0f;
  
  // Newton-Raphson method with good initial guess
  float guess = x;
  guess = 0.5f * (guess + x / guess);
  guess = 0.5f * (guess + x / guess);
  return guess;
}

float fastInvSqrt(float x) {
  if (x <= 0.0f) return 0.0f;
  
  // Fast inverse square root (Quake algorithm)
  long i;
  float x2, y;
  const float threehalfs = 1.5f;
  
  x2 = x * 0.5f;
  y = x;
  i = *(long*)&y;
  i = 0x5f3759df - (i >> 1);
  y = *(float*)&i;
  y = y * (threehalfs - (x2 * y * y));
  return y;
}
