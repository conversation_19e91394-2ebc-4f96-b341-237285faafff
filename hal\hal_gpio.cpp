/*
 * QuadFly Flight Controller - GPIO HAL Implementation
 * 
 * ESP32-specific GPIO hardware abstraction
 */

#include "hal_gpio.h"
#include <driver/gpio.h>

// GPIO callback storage
static HAL_GPIO_Callback gpio_callbacks[GPIO_NUM_MAX] = {NULL};

// Internal interrupt handler
void IRAM_ATTR gpio_isr_handler(void* arg) {
  uint8_t pin = (uint8_t)(uintptr_t)arg;
  if (pin < GPIO_NUM_MAX && gpio_callbacks[pin] != NULL) {
    gpio_callbacks[pin](pin);
  }
}

// Initialize GPIO pin
HAL_StatusTypeDef HAL_GPIO_Init(uint8_t pin, HAL_GPIO_Mode mode) {
  if (!HAL_GPIO_IsValidPin(pin)) {
    return HAL_INVALID_PARAM;
  }

  gpio_config_t io_conf = {};
  io_conf.pin_bit_mask = (1ULL << pin);
  
  switch (mode) {
    case HAL_GPIO_MODE_INPUT:
      io_conf.mode = GPIO_MODE_INPUT;
      io_conf.pull_up_en = GPIO_PULLUP_DISABLE;
      io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
      break;
      
    case HAL_GPIO_MODE_OUTPUT:
      io_conf.mode = GPIO_MODE_OUTPUT;
      io_conf.pull_up_en = GPIO_PULLUP_DISABLE;
      io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
      break;
      
    case HAL_GPIO_MODE_INPUT_PULLUP:
      io_conf.mode = GPIO_MODE_INPUT;
      io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
      io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
      break;
      
    case HAL_GPIO_MODE_INPUT_PULLDOWN:
      io_conf.mode = GPIO_MODE_INPUT;
      io_conf.pull_up_en = GPIO_PULLUP_DISABLE;
      io_conf.pull_down_en = GPIO_PULLDOWN_ENABLE;
      break;
      
    default:
      return HAL_INVALID_PARAM;
  }
  
  io_conf.intr_type = GPIO_INTR_DISABLE;
  
  esp_err_t result = gpio_config(&io_conf);
  return (result == ESP_OK) ? HAL_OK : HAL_ERROR;
}

// Deinitialize GPIO pin
HAL_StatusTypeDef HAL_GPIO_DeInit(uint8_t pin) {
  if (!HAL_GPIO_IsValidPin(pin)) {
    return HAL_INVALID_PARAM;
  }
  
  // Disable interrupt if enabled
  HAL_GPIO_DisableInterrupt(pin);
  
  // Reset pin to default state
  esp_err_t result = gpio_reset_pin((gpio_num_t)pin);
  return (result == ESP_OK) ? HAL_OK : HAL_ERROR;
}

// Write to GPIO pin
void HAL_GPIO_WritePin(uint8_t pin, HAL_GPIO_PinState state) {
  if (HAL_GPIO_IsValidPin(pin)) {
    gpio_set_level((gpio_num_t)pin, (state == HAL_GPIO_PIN_SET) ? 1 : 0);
  }
}

// Read from GPIO pin
HAL_GPIO_PinState HAL_GPIO_ReadPin(uint8_t pin) {
  if (!HAL_GPIO_IsValidPin(pin)) {
    return HAL_GPIO_PIN_RESET;
  }
  
  int level = gpio_get_level((gpio_num_t)pin);
  return (level == 1) ? HAL_GPIO_PIN_SET : HAL_GPIO_PIN_RESET;
}

// Toggle GPIO pin
void HAL_GPIO_TogglePin(uint8_t pin) {
  if (HAL_GPIO_IsValidPin(pin)) {
    HAL_GPIO_PinState current_state = HAL_GPIO_ReadPin(pin);
    HAL_GPIO_WritePin(pin, (current_state == HAL_GPIO_PIN_SET) ? HAL_GPIO_PIN_RESET : HAL_GPIO_PIN_SET);
  }
}

// Configure GPIO pin with advanced options
HAL_StatusTypeDef HAL_GPIO_ConfigPin(const HAL_GPIO_Config* config) {
  if (config == NULL || !HAL_GPIO_IsValidPin(config->pin)) {
    return HAL_INVALID_PARAM;
  }
  
  // Initialize basic GPIO
  HAL_StatusTypeDef status = HAL_GPIO_Init(config->pin, config->mode);
  if (status != HAL_OK) {
    return status;
  }
  
  // Configure interrupt if specified
  if (config->interrupt_type != HAL_GPIO_INTERRUPT_NONE) {
    status = HAL_GPIO_EnableInterrupt(config->pin, config->interrupt_type, config->callback);
  }
  
  return status;
}

// Set GPIO mode
HAL_StatusTypeDef HAL_GPIO_SetMode(uint8_t pin, HAL_GPIO_Mode mode) {
  return HAL_GPIO_Init(pin, mode);
}

// Get GPIO mode (simplified implementation)
HAL_GPIO_Mode HAL_GPIO_GetMode(uint8_t pin) {
  // ESP32 doesn't provide direct mode reading, return default
  return HAL_GPIO_MODE_INPUT;
}

// Enable GPIO interrupt
HAL_StatusTypeDef HAL_GPIO_EnableInterrupt(uint8_t pin, HAL_GPIO_InterruptType type, HAL_GPIO_Callback callback) {
  if (!HAL_GPIO_IsValidPin(pin) || callback == NULL) {
    return HAL_INVALID_PARAM;
  }
  
  gpio_int_type_t esp_int_type;
  
  switch (type) {
    case HAL_GPIO_INTERRUPT_RISING:
      esp_int_type = GPIO_INTR_POSEDGE;
      break;
    case HAL_GPIO_INTERRUPT_FALLING:
      esp_int_type = GPIO_INTR_NEGEDGE;
      break;
    case HAL_GPIO_INTERRUPT_CHANGE:
      esp_int_type = GPIO_INTR_ANYEDGE;
      break;
    case HAL_GPIO_INTERRUPT_LOW:
      esp_int_type = GPIO_INTR_LOW_LEVEL;
      break;
    case HAL_GPIO_INTERRUPT_HIGH:
      esp_int_type = GPIO_INTR_HIGH_LEVEL;
      break;
    default:
      return HAL_INVALID_PARAM;
  }
  
  // Store callback
  gpio_callbacks[pin] = callback;
  
  // Install ISR service if not already installed
  static bool isr_service_installed = false;
  if (!isr_service_installed) {
    esp_err_t result = gpio_install_isr_service(0);
    if (result != ESP_OK) {
      return HAL_ERROR;
    }
    isr_service_installed = true;
  }
  
  // Set interrupt type
  esp_err_t result = gpio_set_intr_type((gpio_num_t)pin, esp_int_type);
  if (result != ESP_OK) {
    return HAL_ERROR;
  }
  
  // Add ISR handler
  result = gpio_isr_handler_add((gpio_num_t)pin, gpio_isr_handler, (void*)(uintptr_t)pin);
  if (result != ESP_OK) {
    return HAL_ERROR;
  }
  
  // Enable interrupt
  result = gpio_intr_enable((gpio_num_t)pin);
  return (result == ESP_OK) ? HAL_OK : HAL_ERROR;
}

// Disable GPIO interrupt
HAL_StatusTypeDef HAL_GPIO_DisableInterrupt(uint8_t pin) {
  if (!HAL_GPIO_IsValidPin(pin)) {
    return HAL_INVALID_PARAM;
  }
  
  // Disable interrupt
  gpio_intr_disable((gpio_num_t)pin);
  
  // Remove ISR handler
  gpio_isr_handler_remove((gpio_num_t)pin);
  
  // Clear callback
  gpio_callbacks[pin] = NULL;
  
  return HAL_OK;
}

// Clear GPIO interrupt
void HAL_GPIO_ClearInterrupt(uint8_t pin) {
  if (HAL_GPIO_IsValidPin(pin)) {
    // ESP32 automatically clears interrupt flags
  }
}

// Get GPIO interrupt status
bool HAL_GPIO_GetInterruptStatus(uint8_t pin) {
  // ESP32 doesn't provide direct interrupt status reading
  return false;
}

// Write to multiple GPIO pins
HAL_StatusTypeDef HAL_GPIO_WritePort(uint32_t port_value, uint32_t pin_mask) {
  // ESP32 doesn't have direct port operations, implement bit by bit
  for (int i = 0; i < 32; i++) {
    if (pin_mask & (1 << i)) {
      HAL_GPIO_PinState state = (port_value & (1 << i)) ? HAL_GPIO_PIN_SET : HAL_GPIO_PIN_RESET;
      HAL_GPIO_WritePin(i, state);
    }
  }
  return HAL_OK;
}

// Read from multiple GPIO pins
uint32_t HAL_GPIO_ReadPort(uint32_t pin_mask) {
  uint32_t port_value = 0;
  
  for (int i = 0; i < 32; i++) {
    if (pin_mask & (1 << i)) {
      if (HAL_GPIO_ReadPin(i) == HAL_GPIO_PIN_SET) {
        port_value |= (1 << i);
      }
    }
  }
  
  return port_value;
}

// Check if pin number is valid
bool HAL_GPIO_IsValidPin(uint8_t pin) {
  // ESP32 valid GPIO pins (excluding input-only and reserved pins)
  return (pin < GPIO_NUM_MAX) && 
         (pin != 6) && (pin != 7) && (pin != 8) && (pin != 9) && 
         (pin != 10) && (pin != 11) && (pin != 20) && (pin != 24) && 
         (pin != 28) && (pin != 29) && (pin != 30) && (pin != 31);
}

// Check if pin is available for use
bool HAL_GPIO_IsPinAvailable(uint8_t pin) {
  // Additional checks for pins that might be in use by system
  if (!HAL_GPIO_IsValidPin(pin)) {
    return false;
  }
  
  // Check for commonly reserved pins
  if (pin == 1 || pin == 3) { // UART0 TX/RX
    return false;
  }
  
  return true;
}

// Check GPIO capabilities
bool HAL_GPIO_SupportsInput(uint8_t pin) {
  return HAL_GPIO_IsValidPin(pin);
}

bool HAL_GPIO_SupportsOutput(uint8_t pin) {
  // Input-only pins on ESP32
  return HAL_GPIO_IsValidPin(pin) && 
         (pin != 34) && (pin != 35) && (pin != 36) && (pin != 39);
}

bool HAL_GPIO_SupportsPullup(uint8_t pin) {
  return HAL_GPIO_IsValidPin(pin);
}

bool HAL_GPIO_SupportsPulldown(uint8_t pin) {
  return HAL_GPIO_IsValidPin(pin);
}

bool HAL_GPIO_SupportsInterrupt(uint8_t pin) {
  return HAL_GPIO_IsValidPin(pin);
}
