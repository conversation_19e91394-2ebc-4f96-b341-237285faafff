/*
 * QuadFly Flight Controller - Motor Control Header
 * 
 * ESC control and motor management system
 */

#ifndef MOTORS_H
#define MOTORS_H

#include "config.h"

// Function declarations
bool initializeMotors(void);
void updateMotorOutputs(MotorData_t *motorData);
void setAllMotorsToMin(void);
void calibrateESCs(void);
void armMotors(void);
void disarmMotors(void);

// Individual motor control
void setMotorSpeed(uint8_t motor, uint16_t speed);

#endif // MOTORS_H