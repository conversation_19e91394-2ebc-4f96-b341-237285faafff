/*
 * QuadFly Flight Controller - Advanced Sensor Fusion Header
 * 
 * Madgwick/Mahony filters, sensor redundancy, health monitoring, and auto-calibration
 */

#ifndef SENSOR_FUSION_H
#define SENSOR_FUSION_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>
#include "math_utils.h"

// Sensor fusion algorithm types
typedef enum {
  FUSION_COMPLEMENTARY = 0,
  FUSION_MADGWICK = 1,
  FUSION_MAHONY = 2,
  FUSION_KALMAN = 3,
  FUSION_ADAPTIVE = 4
} SensorFusionAlgorithm_t;

// Sensor health status
typedef enum {
  SENSOR_HEALTH_EXCELLENT = 100,
  SENSOR_HEALTH_GOOD = 80,
  SENSOR_HEALTH_WARNING = 60,
  SENSOR_HEALTH_POOR = 40,
  SENSOR_HEALTH_CRITICAL = 20,
  SENSOR_HEALTH_FAILED = 0
} SensorHealth_t;

// Sensor types for redundancy
typedef enum {
  SENSOR_TYPE_ACCELEROMETER = 0,
  SENSOR_TYPE_GYROSCOPE = 1,
  SENSOR_TYPE_MAGNETOMETER = 2,
  SENSOR_TYPE_BAROMETER = 3,
  SENSOR_TYPE_GPS = 4,
  SENSOR_TYPE_COUNT = 5
} SensorType_t;

// Individual sensor data with metadata
typedef struct {
  Vector3_t data;
  float temperature;
  uint32_t timestamp;
  SensorHealth_t health;
  float noise_level;
  float bias[3];
  float scale[3];
  bool calibrated;
  bool active;
  uint32_t error_count;
  uint32_t total_samples;
} SensorInstance_t;

// Redundant sensor group
typedef struct {
  SensorInstance_t sensors[4];  // Up to 4 redundant sensors
  uint8_t sensor_count;
  uint8_t primary_sensor;
  Vector3_t fused_data;
  SensorHealth_t group_health;
  bool voting_enabled;
  float agreement_threshold;
} RedundantSensorGroup_t;

// Sensor fusion state
typedef struct {
  Quaternion_t orientation;
  Vector3_t angular_velocity;
  Vector3_t linear_acceleration;
  Vector3_t magnetic_field;
  float altitude;
  float vertical_velocity;
  
  // Fusion algorithm parameters
  SensorFusionAlgorithm_t algorithm;
  float beta;              // Madgwick gain
  float kp, ki;           // Mahony gains
  float sample_rate;
  
  // Quality metrics
  float fusion_confidence;
  float orientation_accuracy;
  float heading_accuracy;
  uint32_t last_update;
  
  // Adaptive parameters
  bool adaptive_gains;
  float dynamic_beta;
  float dynamic_kp;
  
} SensorFusionState_t;

// Calibration state
typedef struct {
  bool in_progress;
  SensorType_t sensor_type;
  uint32_t start_time;
  uint32_t duration_ms;
  uint16_t sample_count;
  uint16_t required_samples;
  
  // Calibration data collection
  Vector3_t min_values;
  Vector3_t max_values;
  Vector3_t sum_values;
  Vector3_t sum_squared;
  
  // Results
  Vector3_t offset;
  Vector3_t scale;
  float quality_score;
  bool completed;
  bool successful;
  
} CalibrationState_t;

// Sensor health monitoring
typedef struct {
  uint32_t last_update;
  uint32_t update_count;
  float update_rate;
  float expected_rate;
  
  // Statistical monitoring
  Vector3_t mean_values;
  Vector3_t variance;
  Vector3_t min_values;
  Vector3_t max_values;
  
  // Anomaly detection
  uint32_t anomaly_count;
  float anomaly_threshold;
  bool stuck_sensor;
  bool noisy_sensor;
  bool drifting_sensor;
  
  // Temperature compensation
  float temperature_coefficient[3];
  float reference_temperature;
  
} SensorHealthMonitor_t;

// Function declarations

// Initialization and configuration
bool SensorFusion_Init(SensorFusionAlgorithm_t algorithm, float sample_rate);
void SensorFusion_SetAlgorithm(SensorFusionAlgorithm_t algorithm);
void SensorFusion_SetSampleRate(float rate);
void SensorFusion_SetGains(float beta, float kp, float ki);
void SensorFusion_EnableAdaptiveGains(bool enable);

// Main fusion update functions
void SensorFusion_Update(const Vector3_t* gyro, const Vector3_t* accel, const Vector3_t* mag, float dt);
void SensorFusion_UpdateIMU(const Vector3_t* gyro, const Vector3_t* accel, float dt);
void SensorFusion_UpdateMag(const Vector3_t* mag);
void SensorFusion_UpdateBarometer(float pressure, float temperature);

// Specific algorithm implementations
void SensorFusion_MadgwickUpdate(const Vector3_t* gyro, const Vector3_t* accel, const Vector3_t* mag, float dt);
void SensorFusion_MahonyUpdate(const Vector3_t* gyro, const Vector3_t* accel, const Vector3_t* mag, float dt);
void SensorFusion_ComplementaryUpdate(const Vector3_t* gyro, const Vector3_t* accel, float dt);
void SensorFusion_KalmanUpdate(const Vector3_t* gyro, const Vector3_t* accel, const Vector3_t* mag, float dt);

// State access
Quaternion_t SensorFusion_GetOrientation(void);
void SensorFusion_GetEulerAngles(float* roll, float* pitch, float* yaw);
Vector3_t SensorFusion_GetAngularVelocity(void);
Vector3_t SensorFusion_GetLinearAcceleration(void);
Vector3_t SensorFusion_GetGravityVector(void);
float SensorFusion_GetHeading(void);

// Redundancy management
bool SensorFusion_RegisterSensor(SensorType_t type, uint8_t sensor_id);
bool SensorFusion_UpdateSensorData(SensorType_t type, uint8_t sensor_id, const Vector3_t* data);
void SensorFusion_EnableSensorVoting(SensorType_t type, bool enable);
void SensorFusion_SetAgreementThreshold(SensorType_t type, float threshold);
Vector3_t SensorFusion_GetFusedSensorData(SensorType_t type);

// Health monitoring
void SensorFusion_InitHealthMonitoring(void);
void SensorFusion_UpdateSensorHealth(SensorType_t type, uint8_t sensor_id);
SensorHealth_t SensorFusion_GetSensorHealth(SensorType_t type, uint8_t sensor_id);
SensorHealth_t SensorFusion_GetOverallHealth(void);
bool SensorFusion_IsSensorFailed(SensorType_t type, uint8_t sensor_id);
void SensorFusion_DisableSensor(SensorType_t type, uint8_t sensor_id);

// Automatic calibration
bool SensorFusion_StartCalibration(SensorType_t type);
bool SensorFusion_UpdateCalibration(SensorType_t type, const Vector3_t* data);
bool SensorFusion_IsCalibrationComplete(SensorType_t type);
CalibrationState_t SensorFusion_GetCalibrationState(SensorType_t type);
bool SensorFusion_ApplyCalibration(SensorType_t type);
void SensorFusion_CancelCalibration(SensorType_t type);

// Calibration procedures
bool SensorFusion_CalibrateAccelerometer(void);
bool SensorFusion_CalibrateGyroscope(void);
bool SensorFusion_CalibrateMagnetometer(void);
bool SensorFusion_CalibrateBarometer(void);

// Quality assessment
float SensorFusion_GetFusionConfidence(void);
float SensorFusion_GetOrientationAccuracy(void);
float SensorFusion_GetHeadingAccuracy(void);
bool SensorFusion_IsDataReliable(void);

// Bias and drift compensation
void SensorFusion_UpdateGyroBias(const Vector3_t* bias);
void SensorFusion_UpdateAccelBias(const Vector3_t* bias);
void SensorFusion_UpdateMagBias(const Vector3_t* bias);
Vector3_t SensorFusion_GetGyroBias(void);
Vector3_t SensorFusion_GetAccelBias(void);
Vector3_t SensorFusion_GetMagBias(void);

// Temperature compensation
void SensorFusion_EnableTemperatureCompensation(SensorType_t type, bool enable);
void SensorFusion_SetTemperatureCoefficients(SensorType_t type, const float* coefficients);
void SensorFusion_UpdateTemperature(SensorType_t type, float temperature);

// Advanced features
void SensorFusion_EnableMagneticDeclination(bool enable, float declination);
void SensorFusion_SetMagneticInclination(float inclination);
void SensorFusion_EnableGravityCompensation(bool enable);
void SensorFusion_SetLocalGravity(float gravity);

// Anomaly detection
bool SensorFusion_DetectSensorAnomaly(SensorType_t type, uint8_t sensor_id);
void SensorFusion_SetAnomalyThreshold(SensorType_t type, float threshold);
uint32_t SensorFusion_GetAnomalyCount(SensorType_t type, uint8_t sensor_id);
void SensorFusion_ResetAnomalyCount(SensorType_t type, uint8_t sensor_id);

// Diagnostic and debugging
void SensorFusion_PrintState(void);
void SensorFusion_PrintSensorHealth(void);
void SensorFusion_PrintCalibrationStatus(void);
void SensorFusion_LogFusionData(void);

// Reset and recovery
void SensorFusion_Reset(void);
void SensorFusion_ResetOrientation(void);
void SensorFusion_ResetCalibration(SensorType_t type);
bool SensorFusion_RecoverFromFailure(void);

// Configuration save/load
bool SensorFusion_SaveConfiguration(void);
bool SensorFusion_LoadConfiguration(void);
void SensorFusion_ResetToDefaults(void);

// Performance optimization
void SensorFusion_EnableFastMode(bool enable);
void SensorFusion_SetUpdateRate(SensorType_t type, float rate);
void SensorFusion_OptimizeForPlatform(void);

#endif // SENSOR_FUSION_H
