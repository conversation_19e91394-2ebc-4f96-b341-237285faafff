/*
 * QuadFly Flight Controller - Telemetry System Implementation
 * 
 * MAVLink-compatible telemetry system for ground station communication
 */

#include "telemetry.h"

// Initialize telemetry system
void initializeTelemetry(void) {
  Serial.println("Initializing telemetry system...");
  
  // Telemetry serial port initialized in main setup
  Serial.println("Telemetry system initialized");
}

// Send telemetry data
void sendTelemetryData(FlightData_t *flightData, SystemState_t *systemState) {
  static uint32_t lastHeartbeat = 0;
  static uint32_t lastAttitude = 0;
  static uint32_t lastGPS = 0;
  static uint32_t lastBattery = 0;
  static uint32_t lastStatus = 0;
  
  uint32_t currentTime = millis();
  
  // Send heartbeat every 1 second
  if (currentTime - lastHeartbeat >= 1000) {
    sendHeartbeat(systemState);
    lastHeartbeat = currentTime;
  }
  
  // Send attitude data every 100ms
  if (currentTime - lastAttitude >= 100) {
    sendAttitudeData(&flightData->attitude);
    lastAttitude = currentTime;
  }
  
  // Send GPS data every 500ms
  if (currentTime - lastGPS >= 500) {
    sendGPSData(&flightData->gps);
    lastGPS = currentTime;
  }
  
  // Send battery data every 1 second
  if (currentTime - lastBattery >= 1000) {
    sendBatteryData(&flightData->battery);
    lastBattery = currentTime;
  }
  
  // Send system status every 2 seconds
  if (currentTime - lastStatus >= 2000) {
    sendSystemStatus(systemState);
    lastStatus = currentTime;
  }
}

// Send heartbeat message
void sendHeartbeat(SystemState_t *systemState) {
  Serial2.write(0xFE); // MAVLink start byte
  Serial2.write(9);    // Payload length
  Serial2.write(0);    // Packet sequence
  Serial2.write(1);    // System ID
  Serial2.write(1);    // Component ID
  Serial2.write(TELEM_HEARTBEAT); // Message ID
  
  // Payload
  Serial2.write(systemState->flightMode);
  Serial2.write(systemState->armed ? 1 : 0);
  Serial2.write(systemState->systemHealth);
  Serial2.write((uint8_t)(systemState->uptime >> 24));
  Serial2.write((uint8_t)(systemState->uptime >> 16));
  Serial2.write((uint8_t)(systemState->uptime >> 8));
  Serial2.write((uint8_t)(systemState->uptime));
  Serial2.write((uint8_t)(systemState->flightTime >> 8));
  Serial2.write((uint8_t)(systemState->flightTime));
  
  // Simple checksum
  uint8_t checksum = 0;
  checksum ^= 9 ^ 0 ^ 1 ^ 1 ^ TELEM_HEARTBEAT;
  checksum ^= systemState->flightMode ^ (systemState->armed ? 1 : 0) ^ systemState->systemHealth;
  checksum ^= (uint8_t)(systemState->uptime >> 24) ^ (uint8_t)(systemState->uptime >> 16);
  checksum ^= (uint8_t)(systemState->uptime >> 8) ^ (uint8_t)(systemState->uptime);
  checksum ^= (uint8_t)(systemState->flightTime >> 8) ^ (uint8_t)(systemState->flightTime);
  
  Serial2.write(checksum);
}

// Send attitude data
void sendAttitudeData(AttitudeData_t *attitude) {
  Serial2.write(0xFE); // MAVLink start byte
  Serial2.write(24);   // Payload length
  Serial2.write(0);    // Packet sequence
  Serial2.write(1);    // System ID
  Serial2.write(1);    // Component ID
  Serial2.write(TELEM_ATTITUDE); // Message ID
  
  // Convert floats to bytes (simplified)
  union {
    float f;
    uint8_t b[4];
  } converter;
  
  // Roll
  converter.f = attitude->roll;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Pitch
  converter.f = attitude->pitch;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Yaw
  converter.f = attitude->yaw;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Roll rate
  converter.f = attitude->rollRate;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Pitch rate
  converter.f = attitude->pitchRate;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Yaw rate
  converter.f = attitude->yawRate;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Simple checksum (simplified for brevity)
  Serial2.write(0x55);
}

// Send GPS data
void sendGPSData(GPSData_t *gps) {
  Serial2.write(0xFE); // MAVLink start byte
  Serial2.write(28);   // Payload length
  Serial2.write(0);    // Packet sequence
  Serial2.write(1);    // System ID
  Serial2.write(1);    // Component ID
  Serial2.write(TELEM_GPS); // Message ID
  
  // Convert doubles and floats to bytes (simplified)
  union {
    double d;
    uint8_t b[8];
  } doubleConverter;
  
  union {
    float f;
    uint8_t b[4];
  } floatConverter;
  
  // Latitude
  doubleConverter.d = gps->latitude;
  for (int i = 0; i < 8; i++) Serial2.write(doubleConverter.b[i]);
  
  // Longitude
  doubleConverter.d = gps->longitude;
  for (int i = 0; i < 8; i++) Serial2.write(doubleConverter.b[i]);
  
  // Altitude
  floatConverter.f = gps->altitude;
  for (int i = 0; i < 4; i++) Serial2.write(floatConverter.b[i]);
  
  // Speed
  floatConverter.f = gps->speed;
  for (int i = 0; i < 4; i++) Serial2.write(floatConverter.b[i]);
  
  // Course
  floatConverter.f = gps->course;
  for (int i = 0; i < 4; i++) Serial2.write(floatConverter.b[i]);
  
  // Fix status and satellite count
  Serial2.write(gps->fix ? 1 : 0);
  Serial2.write(gps->satellites);
  Serial2.write((uint8_t)(gps->hdop * 100)); // HDOP * 100
  Serial2.write(0); // Reserved
  
  // Simple checksum
  Serial2.write(0xAA);
}

// Send battery data
void sendBatteryData(BatteryData_t *battery) {
  Serial2.write(0xFE); // MAVLink start byte
  Serial2.write(12);   // Payload length
  Serial2.write(0);    // Packet sequence
  Serial2.write(1);    // System ID
  Serial2.write(1);    // Component ID
  Serial2.write(TELEM_BATTERY); // Message ID
  
  // Convert floats to bytes
  union {
    float f;
    uint8_t b[4];
  } converter;
  
  // Voltage
  converter.f = battery->voltage;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Current
  converter.f = battery->current;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Capacity
  converter.f = battery->capacity;
  for (int i = 0; i < 4; i++) Serial2.write(converter.b[i]);
  
  // Percentage and status
  Serial2.write(battery->percentage);
  Serial2.write(battery->lowVoltageWarning ? 1 : 0);
  Serial2.write(battery->criticalVoltageWarning ? 1 : 0);
  Serial2.write(0); // Reserved
  
  // Simple checksum
  Serial2.write(0xBB);
}

// Send system status
void sendSystemStatus(SystemState_t *systemState) {
  Serial2.write(0xFE); // MAVLink start byte
  Serial2.write(8);    // Payload length
  Serial2.write(0);    // Packet sequence
  Serial2.write(1);    // System ID
  Serial2.write(1);    // Component ID
  Serial2.write(TELEM_SYSTEM_STATUS); // Message ID
  
  // System status data
  Serial2.write(systemState->flightMode);
  Serial2.write(systemState->armed ? 1 : 0);
  Serial2.write(systemState->emergencyMode ? 1 : 0);
  Serial2.write(systemState->gpsLock ? 1 : 0);
  Serial2.write(systemState->receiverConnected ? 1 : 0);
  Serial2.write(systemState->systemHealth);
  Serial2.write((uint8_t)(systemState->uptime / 1000)); // Uptime in seconds (LSB)
  Serial2.write((uint8_t)((systemState->uptime / 1000) >> 8)); // Uptime in seconds (MSB)
  
  // Simple checksum
  Serial2.write(0xCC);
}

// Process incoming telemetry commands
void processTelemetryCommands(SystemState_t *systemState, ConfigData_t *config) {
  if (Serial2.available()) {
    uint8_t command = Serial2.read();
    
    switch (command) {
      case 0x10: // Request parameter download
        // Send all parameters
        break;
        
      case 0x11: // Set parameter
        // Read parameter ID and value
        break;
        
      case 0x20: // Calibrate sensors
        systemState->calibrationMode = true;
        break;
        
      case 0x30: // Emergency stop
        systemState->emergencyMode = true;
        break;
        
      case 0x40: // Reset flight controller
        ESP.restart();
        break;
        
      default:
        // Unknown command
        break;
    }
  }
}