/*
 * QuadFly Flight Controller - Hardware Abstraction Layer Header
 * 
 * Main HAL interface for hardware independence
 */

#ifndef HAL_H
#define HAL_H

#include <Arduino.h>
#include <stdint.h>
#include <stdbool.h>

// HAL return codes
typedef enum {
  HAL_OK = 0,
  HAL_ERROR = 1,
  HAL_BUSY = 2,
  HAL_TIMEOUT = 3,
  HAL_INVALID_PARAM = 4,
  HAL_NOT_SUPPORTED = 5
} HAL_StatusTypeDef;

// GPIO definitions
typedef enum {
  HAL_GPIO_PIN_RESET = 0,
  HAL_GPIO_PIN_SET = 1
} HAL_GPIO_PinState;

typedef enum {
  HAL_GPIO_MODE_INPUT = 0,
  HAL_GPIO_MODE_OUTPUT = 1,
  HAL_GPIO_MODE_INPUT_PULLUP = 2,
  HAL_GPIO_MODE_INPUT_PULLDOWN = 3
} HAL_GPIO_Mode;

// PWM definitions
typedef struct {
  uint8_t pin;
  uint32_t frequency;
  uint8_t resolution;
  uint8_t channel;
} HAL_PWM_Config;

// I2C definitions
typedef struct {
  uint8_t sda_pin;
  uint8_t scl_pin;
  uint32_t frequency;
  uint8_t timeout_ms;
} HAL_I2C_Config;

// UART definitions
typedef struct {
  uint8_t rx_pin;
  uint8_t tx_pin;
  uint32_t baudrate;
  uint8_t data_bits;
  uint8_t stop_bits;
  uint8_t parity;
} HAL_UART_Config;

// SPI definitions
typedef struct {
  uint8_t miso_pin;
  uint8_t mosi_pin;
  uint8_t sclk_pin;
  uint8_t cs_pin;
  uint32_t frequency;
  uint8_t mode;
} HAL_SPI_Config;

// ADC definitions
typedef struct {
  uint8_t pin;
  uint8_t resolution;
  uint8_t attenuation;
} HAL_ADC_Config;

// Timer definitions
typedef struct {
  uint8_t timer_id;
  uint32_t frequency;
  bool auto_reload;
} HAL_Timer_Config;

// Include platform-specific HAL modules
#include "hal_gpio.h"
#include "hal_pwm.h"
//#include "hal_i2c.h"
//#include "hal_uart.h"
//#include "hal_spi.h"
//#include "hal_adc.h"
//#include "hal_timer.h"
//#include "hal_system.h"

// HAL initialization and management
HAL_StatusTypeDef HAL_Init(void);
HAL_StatusTypeDef HAL_DeInit(void);
void HAL_Delay(uint32_t delay_ms);
void HAL_DelayMicroseconds(uint32_t delay_us);
uint32_t HAL_GetTick(void);
uint64_t HAL_GetMicros(void);

// System control
void HAL_SystemReset(void);
void HAL_SystemSleep(void);
void HAL_SystemDeepSleep(uint32_t sleep_time_ms);

// Watchdog
HAL_StatusTypeDef HAL_Watchdog_Init(uint32_t timeout_ms);
void HAL_Watchdog_Refresh(void);
void HAL_Watchdog_Stop(void);

// Interrupt management
void HAL_EnableInterrupts(void);
void HAL_DisableInterrupts(void);
void HAL_EnterCriticalSection(void);
void HAL_ExitCriticalSection(void);

// Memory management
void* HAL_Malloc(size_t size);
void HAL_Free(void* ptr);
void* HAL_Realloc(void* ptr, size_t size);
uint32_t HAL_GetFreeHeap(void);
uint32_t HAL_GetMinFreeHeap(void);

// Flash memory
HAL_StatusTypeDef HAL_Flash_Read(uint32_t address, uint8_t* data, uint32_t size);
HAL_StatusTypeDef HAL_Flash_Write(uint32_t address, const uint8_t* data, uint32_t size);
HAL_StatusTypeDef HAL_Flash_Erase(uint32_t address, uint32_t size);

// EEPROM emulation
HAL_StatusTypeDef HAL_EEPROM_Read(uint32_t address, uint8_t* data, uint32_t size);
HAL_StatusTypeDef HAL_EEPROM_Write(uint32_t address, const uint8_t* data, uint32_t size);
HAL_StatusTypeDef HAL_EEPROM_Commit(void);

// Random number generation
uint32_t HAL_GetRandomNumber(void);
void HAL_GetRandomBytes(uint8_t* buffer, uint32_t size);

// CRC calculation
uint16_t HAL_CRC16(const uint8_t* data, uint32_t length);
uint32_t HAL_CRC32(const uint8_t* data, uint32_t length);

// Platform information
const char* HAL_GetPlatformName(void);
const char* HAL_GetChipModel(void);
uint32_t HAL_GetChipRevision(void);
uint32_t HAL_GetCpuFrequency(void);
uint32_t HAL_GetFlashSize(void);
const char* HAL_GetMacAddress(void);

#endif // HAL_H
