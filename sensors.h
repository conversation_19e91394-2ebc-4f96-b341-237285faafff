/*
 * QuadFly Flight Controller - Sensor Management Header
 * 
 * Handles all sensor operations including IMU, barometer, and magnetometer
 * Special support for GY-86 module bypass mode
 */

#ifndef SENSORS_H
#define SENSORS_H

#include "config.h"

// MPU6050 register definitions for bypass mode
#define MPU6050_RA_USER_CTRL        0x6A
#define MPU6050_RA_INT_PIN_CFG      0x37
#define MPU6050_USERCTRL_I2C_MST_EN_BIT 5
#define MPU6050_INTCFG_I2C_BYPASS_EN_BIT 1

// Function declarations
bool initializeSensors(void);
bool initializeMPU6050(void);  // Make sure this line exists
bool initializeHMC5883L(void);
bool initializeQMC5883L(void);
bool initializeMagnetometer(void);
bool initializeMS5611(void);
void readSensors(SensorData_t *sensorData);
void updateAttitudeEstimation(SensorData_t *sensors, AttitudeData_t *attitude);
void updateAltitudeEstimation(SensorData_t *sensors, AltitudeData_t *altitude);
void updateSystemHealth(SystemState_t *state);

// GY-86 specific bypass mode functions
bool setMPU6050BypassMode(bool enable);
bool configureMPU6050AuxI2C(void);
bool initializeGY86Module(void);

// MPU6050 functions
void readMPU6050(SensorData_t *sensorData);
void calibrateMPU6050(SensorData_t *sensorData);
bool writeMPU6050Register(uint8_t reg, uint8_t data);
uint8_t readMPU6050Register(uint8_t reg);

// HMC5883L functions
void readHMC5883L(SensorData_t *sensorData);
void calibrateHMC5883L(SensorData_t *sensorData);

// MS5611 functions
void readMS5611(SensorData_t *sensorData);
void calibrateMS5611(SensorData_t *sensorData);

// Attitude estimation functions
void mahonyAHRSUpdate(float gx, float gy, float gz, float ax, float ay, float az, 
                      float mx, float my, float mz, float *q);
void quaternionToEuler(float *q, float *roll, float *pitch, float *yaw);

#endif // SENSORS_H